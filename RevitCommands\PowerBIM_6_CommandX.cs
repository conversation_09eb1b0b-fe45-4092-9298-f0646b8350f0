﻿using Autodesk.Revit.Attributes;
using Autodesk.Revit.DB;
using Autodesk.Revit.UI;
using BecaCommand;
using System;
using System.Collections.Generic;
using System.Linq;
using System.IO;
using System.Text;
using System.Threading.Tasks;
using Autodesk.Revit.DB.Electrical;
using Autodesk.Revit.ApplicationServices;
using Common.UI.Forms;
using BecaTransactionsNamesManager;
using System.Diagnostics;
using System.Windows.Forms;
using BecaRevitUtilities.ElementUtilities;
using TaskDialog = Autodesk.Revit.UI.TaskDialog;
using Autodesk.Revit.UI.Selection;
using System.Net;
using MEP.PowerBIM_5.CoreLogic;
using MEP.PowerBIM_5.UI.Forms;

namespace MEP.PowerBIM_6.RevitCommands
{
    [Transaction(TransactionMode.Manual)]
    public class PowerBIM_6_CommandX : BecaBaseCommand
    {
        public PowerBIM_ProjectInfo projInfo;
        public List<PowerBIM_DBData> DBs;

        public override Result ExecuteBecaCommand(ExternalCommandData commandData, ref string message, ElementSet elements)
        {
            UIApplication uiapp = commandData.Application;
            UIDocument uidoc = uiapp.ActiveUIDocument;
            Document doc = uidoc.Document;
            var app = uiapp.Application;

            try
            {
                if (!RequiredElementsExists(doc))
                {
                    return Result.Cancelled;
                }

                var selectedDBs = GetSelectedElectricalEquipment(uidoc);

                // Parameter checks
                var parameterChecks = new PowerBIM_ParameterCheck(uiapp.ActiveUIDocument.Document);
                if (!parameterChecks.RequiredSharedParameterFileExists)
                {
                    return Result.Cancelled;
                }

                FormCollection fc = System.Windows.Forms.Application.OpenForms;
                foreach (System.Windows.Forms.Form frm in fc)
                {
                    if (frm.Name == "frmPowerBIM_Start")
                    {
                        MEP.PowerBIM_5.UI.Forms.ModelessPowerBIM_StartFormHandler.ShowMsgToTheUser("Info", "PowerBIM is already opened");
                        frm.BringToFront();
                        _taskLogger.PostTaskEnd("powerBim Already Opened");
                        return Result.Cancelled;
                    }
                }

                // Start tracker
                _taskLogger.PreTaskStart();

                if (Properties.Settings.Default.DontShow)
                {
                    var startMessage = new FrmStartMessage();
                    startMessage.ShowDialog();

                    // if user is scared by the beta warning and wants to cancel...
                    if (startMessage.DialogResult == DialogResult.Cancel)
                    {
                        _taskLogger.PostTaskEnd("PowerBim was canceled.");

                        return Result.Cancelled;
                    }
                }

                // create instance of PowerBIM ProjectInfo class for global settings
                projInfo = new PowerBIM_ProjectInfo(uidoc, _taskLogger);

                if (projInfo.restartFlag)
                {
                    MEP.PowerBIM_5.UI.Forms.ModelessPowerBIM_StartFormHandler.ShowDialogMsgToTheUser("Info - Parameters Added", "Restart of PowerBIM is required");
                    _taskLogger.PostTaskEnd("Parameters Added and powerBIM needs restart.");
                    return Autodesk.Revit.UI.Result.Succeeded;
                }


                if (!projInfo.DatabaseArrayIsPopulated)
                {
                    MEP.PowerBIM_5.UI.Forms.ModelessPowerBIM_StartFormHandler.ShowMsgToTheUser("Critical Error", "\nUnable to run PowerBIM. Please check that your PowerBIM 5 database files exist!");
                    _taskLogger.PostTaskEnd("Unable to run PowerBIM.");

                    return Autodesk.Revit.UI.Result.Failed;
                }

                if (projInfo.CriticalErrorMessage != "")
                {
                    MEP.PowerBIM_5.UI.Forms.ModelessPowerBIM_StartFormHandler.ShowMsgToTheUser("Critical Error", "Unable to run PowerBIM. Revit is missing the following parameter(s): \n\n" + projInfo.CriticalErrorMessage);
                    _taskLogger.PostTaskEnd("PowerBim Failed to process.");
                    return Autodesk.Revit.UI.Result.Failed;
                }

                //Get DB Panel Schedules
                FilteredElementCollector PanelSchCol = new FilteredElementCollector(projInfo.Document).OfClass(typeof(PanelScheduleView));
                IList<Element> PanelSchs = PanelSchCol.ToElements() as List<Element>;

                // Fill DBs
                DBs = new List<PowerBIM_DBData>();

                int nCount = PanelSchs.Count();
                string progressMessage = "{0} of " + nCount.ToString() + " panels processed...";
                string caption = "Loading Panel Schedules";
                using (BecaProgressForm pf = new BecaProgressForm(caption, progressMessage, nCount))
                {
                    using (var trans_CCT = new Transaction(projInfo.Document, BecaTransactionsNames.PowerBIM_UpdateDBData.GetHumanReadableString()))
                    {
                        trans_CCT.Start();
                        if (selectedDBs != null && selectedDBs.Count != 0)
                        {
                            foreach (var selectedDB in selectedDBs)
                            {
                                // create new DB class
                                PowerBIM_DBData x = new PowerBIM_DBData(projInfo, selectedDB);
                                // Initialise all circuits
                                x.Initialise_AllCircuits();
                                // add to list of DBs in project
                                DBs.Add(x);
                                pf.Increment();
                            }
                        }
                        else
                        {
                            // Create list of PowerBIM_DBData class for every panel schedule in the project/
                            foreach (PanelScheduleView rqPnlSch in PanelSchs)
                            {
                                Element PnlElem = projInfo.Document.GetElement(rqPnlSch.GetPanel());
                                // create new DB class
                                PowerBIM_DBData x = new PowerBIM_DBData(projInfo, PnlElem);
                                // Initialise all circuits
                                x.Initialise_AllCircuits();
                                // add to list of DBs in project
                                DBs.Add(x);
                                pf.Increment();
                            }
                        }
                        trans_CCT.Commit();
                    }
                }

                // sort list list of PowerBIM_DBData classes alphabetical (descending) order
                IOrderedEnumerable<PowerBIM_DBData> orderedDBs = from PowerBIM_DBData DB in DBs orderby DB.Schedule_DB_Name ascending select DB;

                // Check that there is at least 1 DB set up
                if (DBs.Count < 1)
                {
                    //UI.Forms.ModelessPowerBIM_StartFormHandler.ShowMsgToTheUser("PowerBIM", "PowerBIM Error: Your project has no DB schedules yet!");
                    _taskLogger.PostTaskEnd("Your project has no DB schedules yet.");
                    return Autodesk.Revit.UI.Result.Cancelled;
                }

                //Check the current view and get the DB name for that
                string strCurrentPanelName = "";
                if (projInfo.Document.ActiveView.ViewType == Autodesk.Revit.DB.ViewType.PanelSchedule)
                {
                    PanelScheduleView rqCurrentPanelView = projInfo.Document.ActiveView as PanelScheduleView;
                    strCurrentPanelName = projInfo.Document.GetElement(rqCurrentPanelView.GetPanel()).Name;
                }

                // convert enumerable back to list
                DBs = orderedDBs.ToList();

                //Find the position of currently viewed panel schedule, as we want this to be highlighted in the dialogue when we launch powerbim
                int intPosCount = 0;
                foreach (PowerBIM_DBData DB in DBs)
                {
                    if (DB.Schedule_DB_Name == strCurrentPanelName)
                        projInfo.GUI_Gen_DB_Selected_Position = intPosCount;

                    intPosCount++;
                }

                // Show user locked elements info
                ShowAndExportLockedElementsInfo();

                var dBs = DBs;
                var projectInfo = projInfo;
                
                //// Modeless form
                //ModelessPowerBIM_StartFormHandler.ShowForm(DBs, projInfo, _taskLogger);

                // If user selects 'close'- this is the only way to exit the application

                // Successfully completed PowerBIM processes!!  
                _taskLogger.PostTaskEnd("PowerBim: check detailed logs.");

                // Return application succeeded
                return Autodesk.Revit.UI.Result.Succeeded;
            }
            catch (Exception e)
            {
                MEP.PowerBIM_5.UI.Forms.ModelessPowerBIM_StartFormHandler.ShowMsgToTheUser("PowerBIM Failed", e.Message);
                throw;
            }

            return Result.Succeeded;
        }

        private void ShowAndExportLockedElementsInfo()
        {
            var sb = new StringBuilder();
            sb.AppendLine("Checked at: " + DateTime.Now.ToString() + "\n");
            var sbTaskDialog = new StringBuilder();

            var lockedDBs = DBs.Where(x => x.DB_Element.IsLocked(projInfo.Document));
            var lockedCCTs = DBs.SelectMany(x => x.CCTs).Where(y => y.IsLocked).Select(z => z).GroupBy(u => u.DB);
            foreach (var dB in lockedDBs)
            {
                sb.AppendLine(dB.Schedule_DB_Name + " is locked out by: " + dB.DB_Element.ElementOwner(projInfo.Document));
            }
            var sbOwnersDB = new StringBuilder();
            lockedDBs.Select(x => x.DB_Element.ElementOwner(projInfo.Document)).Distinct().ToList().ForEach(y => sbOwnersDB.AppendLine(y));
            sbTaskDialog.AppendLine(lockedDBs.Count() + " DBs are locked out by:\n" + sbOwnersDB.ToString());
            foreach (var dB in lockedCCTs)
            {
                sb.AppendLine("DB Name: " + dB.Key.Schedule_DB_Name + ", Id:" + dB.Key.DB_Element.Id.IntegerValue.ToString());
                var sbOwners = new StringBuilder();
                foreach (var cct in dB)
                {
                    sb.AppendLine(cct.CCT_Number + " is locked out by: " + cct.LockedOwnerName);
                }
                foreach (var cct in dB.Key.CCTs.GroupBy(x => x.LockedOwnerName).Select(y => y.First()))
                {
                    sbOwners.AppendLine(cct.LockedOwnerName);
                }
                sbTaskDialog.AppendLine(dB.Count() + " circuits in " + dB.Key.Schedule_DB_Name + " are locked out by:\n" + sbOwners.ToString());
            }

            if (lockedCCTs.Count() > 0 || lockedDBs.Count() > 0)
            {
                //TaskDialog.Show("Info", sbTaskDialog.ToString());
                TaskDialog dialog = new TaskDialog("Circuit locked out info");
                dialog.MainContent = sbTaskDialog.ToString() + "Do you want to generate a list of locked circuit?";
                dialog.CommonButtons = TaskDialogCommonButtons.Yes | TaskDialogCommonButtons.No;
                dialog.DefaultButton = TaskDialogResult.No;
                TaskDialogResult result = dialog.Show();
                if (result == TaskDialogResult.Yes)
                {
                    try
                    {
                        var filename = projInfo.LockedCircuitInfo_TXT;
                        File.WriteAllText(filename, sb.ToString());
                        Process.Start(filename);
                    }
                    catch (Exception e)
                    {
                        TaskDialog.Show("Exception", e.Message);
                    }
                }
            }
        }

        public bool RequiredElementsExists(Document doc)
        {
            FilteredElementCollector electricalEquipmentCollector =
                new FilteredElementCollector(doc).OfCategory(BuiltInCategory.OST_ElectricalEquipment)
                .WhereElementIsNotElementType();

            bool hasElectricalEquipment = electricalEquipmentCollector.Any();

            FilteredElementCollector electricalCircuitCollector =
                new FilteredElementCollector(doc).OfCategory(BuiltInCategory.OST_ElectricalCircuit)
                .WhereElementIsNotElementType();

            bool hasElectricalCircuit = electricalCircuitCollector.Any();

            var panelScheduleCollector =
                new FilteredElementCollector(doc).OfClass(typeof(PanelScheduleView));

            bool hasPanelSchedule = panelScheduleCollector.Any();

            List<string> missingCategories = new List<string>();

            if (!hasElectricalEquipment)
            {
                missingCategories.Add("Electrical Equipment");
            }

            if (!hasElectricalCircuit)
            {
                missingCategories.Add("Electrical Circuits");
            }

            if (!hasPanelSchedule)
            {
                missingCategories.Add("Panel Schedules");
            }

            if (missingCategories.Count > 0)
            {
                MessageBox.Show($"Missing: {string.Join(", ", missingCategories)}.\n\nPlease add the required elements.", "Missing Required Elements");
                return false;
            }

            return true;
        }
        public List<Element> GetSelectedElectricalEquipment(UIDocument uiDoc)
        {
            var doc = uiDoc.Document;
            var panelIds = new FilteredElementCollector(doc).OfClass(typeof(PanelScheduleView)).Cast<PanelScheduleView>().Select(p => p.GetPanel()).ToList();
            ICollection<ElementId> selectedIds = uiDoc.Selection.GetElementIds();

            if (selectedIds.Count == 0)
            {
                return null;
            }

            List<Element> matchingElectricalEquipment = new List<Element>();

            foreach (ElementId id in selectedIds)
            {
                Element element = doc.GetElement(id);

                if (element.Category != null && element.Category.Id == new ElementId(BuiltInCategory.OST_ElectricalEquipment))
                {
                    if (panelIds.Contains(element.Id))
                    {
                        matchingElectricalEquipment.Add(element);
                    }
                }
            }

            if (matchingElectricalEquipment.Count == 0)
            {
                TaskDialog.Show("Missing DB selection", "Please select a valid DB.");
            }

            return matchingElectricalEquipment;
        }

        public override string GetAddinAuthor()
        {
            return "Tristan Balme, Harry Billinge, Firza Utama";
        }

        public override string GetAddinName()
        {
            return AddinNames.PowerBIM6.Value;
        }

        public override string GetCommandSubName()
        {
            return string.Empty;
        }
    }
}
