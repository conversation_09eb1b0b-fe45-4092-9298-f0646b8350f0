﻿using System;
using System.Globalization;
using System.Windows.Data;
using System.Windows.Media;
using Brush = System.Windows.Media.Brush;

namespace MEP.PowerBIM_6.Converters
{
    public class StatusColorConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is string status)
            {
                switch (status.ToLower())
                {
                    case "pass":
                    case "green":
                        return new SolidColorBrush(Colors.Green);
                    case "warning":
                    case "orange":
                        return new SolidColorBrush(Colors.Orange);
                    case "fail":
                    case "red":
                        return new SolidColorBrush(Colors.Red);
                    case "gray":
                    case "grey":
                        return new SolidColorBrush(Colors.Gray);
                    default:
                        return new SolidColorBrush(Colors.Transparent);
                }
            }

            return new SolidColorBrush(Colors.Transparent);
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    /// <summary>
    /// Converts boolean values to colors
    /// </summary>
    public class BooleanToColorConverter : IValueConverter
    {
        public Brush TrueBrush { get; set; } = new SolidColorBrush(Colors.Green);
        public Brush FalseBrush { get; set; } = new SolidColorBrush(Colors.Red);

        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool boolValue)
            {
                return boolValue ? TrueBrush : FalseBrush;
            }

            return FalseBrush;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    /// <summary>
    /// Converts numeric values to colors based on thresholds
    /// </summary>
    public class NumericToColorConverter : IValueConverter
    {
        public double WarningThreshold { get; set; } = 0;
        public double ErrorThreshold { get; set; } = 0;
        public Brush GoodBrush { get; set; } = new SolidColorBrush(Colors.Green);
        public Brush WarningBrush { get; set; } = new SolidColorBrush(Colors.Orange);
        public Brush ErrorBrush { get; set; } = new SolidColorBrush(Colors.Red);

        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is double doubleValue)
            {
                if (ErrorThreshold > 0 && doubleValue >= ErrorThreshold)
                    return ErrorBrush;
                if (WarningThreshold > 0 && doubleValue >= WarningThreshold)
                    return WarningBrush;
                return GoodBrush;
            }

            if (value is int intValue)
            {
                if (ErrorThreshold > 0 && intValue >= ErrorThreshold)
                    return ErrorBrush;
                if (WarningThreshold > 0 && intValue >= WarningThreshold)
                    return WarningBrush;
                return GoodBrush;
            }

            return GoodBrush;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
