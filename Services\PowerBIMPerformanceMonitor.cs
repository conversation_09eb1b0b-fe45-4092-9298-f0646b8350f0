﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MEP.PowerBIM_6.Services
{
    /// <summary>
    /// Performance monitoring service for PowerBIM operations
    /// </summary>
    public class PowerBIMPerformanceMonitor
    {
        #region Performance Metrics

        public class PerformanceMetric
        {
            public string OperationName { get; set; }
            public DateTime StartTime { get; set; }
            public DateTime EndTime { get; set; }
            public TimeSpan Duration => EndTime - StartTime;
            public long MemoryBefore { get; set; }
            public long MemoryAfter { get; set; }
            public long MemoryUsed => MemoryAfter - MemoryBefore;
            public bool IsSuccess { get; set; }
            public string ErrorMessage { get; set; }
            public Dictionary<string, object> AdditionalData { get; set; } = new Dictionary<string, object>();
        }

        public class PerformanceReport
        {
            public List<PerformanceMetric> Metrics { get; set; } = new List<PerformanceMetric>();
            public DateTime ReportTime { get; set; } = DateTime.Now;
            public TimeSpan TotalDuration => Metrics.Any() ?
                Metrics.Max(m => m.EndTime) - Metrics.Min(m => m.StartTime) : TimeSpan.Zero;
            public long TotalMemoryUsed => Metrics.Sum(m => m.MemoryUsed);
            public int SuccessfulOperations => Metrics.Count(m => m.IsSuccess);
            public int FailedOperations => Metrics.Count(m => !m.IsSuccess);
        }

        #endregion

        #region Private Fields

        private readonly List<PerformanceMetric> _metrics = new List<PerformanceMetric>();
        private readonly Dictionary<string, Stopwatch> _activeOperations = new Dictionary<string, Stopwatch>();
        private readonly Dictionary<string, long> _operationMemoryStart = new Dictionary<string, long>();

        #endregion

        #region Public Methods

        /// <summary>
        /// Starts monitoring an operation
        /// </summary>
        public void StartOperation(string operationName)
        {
            var stopwatch = Stopwatch.StartNew();
            var memoryBefore = GC.GetTotalMemory(false);

            _activeOperations[operationName] = stopwatch;
            _operationMemoryStart[operationName] = memoryBefore;

            Debug.WriteLine($"[PERF] Started: {operationName} at {DateTime.Now:HH:mm:ss.fff}");
        }

        /// <summary>
        /// Ends monitoring an operation
        /// </summary>
        public PerformanceMetric EndOperation(string operationName, bool isSuccess = true, string errorMessage = null)
        {
            if (!_activeOperations.TryGetValue(operationName, out var stopwatch))
            {
                Debug.WriteLine($"[PERF] Warning: Operation '{operationName}' was not started");
                return null;
            }

            stopwatch.Stop();
            var memoryAfter = GC.GetTotalMemory(false);
            var memoryBefore = _operationMemoryStart.TryGetValue(operationName, out var memory) ? memory : 0;

            var metric = new PerformanceMetric
            {
                OperationName = operationName,
                StartTime = DateTime.Now - stopwatch.Elapsed,
                EndTime = DateTime.Now,
                MemoryBefore = memoryBefore,
                MemoryAfter = memoryAfter,
                IsSuccess = isSuccess,
                ErrorMessage = errorMessage
            };

            _metrics.Add(metric);
            _activeOperations.Remove(operationName);
            _operationMemoryStart.Remove(operationName);

            Debug.WriteLine($"[PERF] Completed: {operationName} in {metric.Duration.TotalMilliseconds:F2}ms " +
                          $"(Memory: {FormatBytes(metric.MemoryUsed)})");

            return metric;
        }

        /// <summary>
        /// Measures the performance of an operation
        /// </summary>
        public async Task<T> MeasureAsync<T>(string operationName, Func<Task<T>> operation)
        {
            StartOperation(operationName);

            try
            {
                var result = await operation();
                EndOperation(operationName, true);
                return result;
            }
            catch (Exception ex)
            {
                EndOperation(operationName, false, ex.Message);
                throw;
            }
        }

        /// <summary>
        /// Measures the performance of a synchronous operation
        /// </summary>
        public T Measure<T>(string operationName, Func<T> operation)
        {
            StartOperation(operationName);

            try
            {
                var result = operation();
                EndOperation(operationName, true);
                return result;
            }
            catch (Exception ex)
            {
                EndOperation(operationName, false, ex.Message);
                throw;
            }
        }

        /// <summary>
        /// Measures the performance of an action
        /// </summary>
        public void Measure(string operationName, Action operation)
        {
            StartOperation(operationName);

            try
            {
                operation();
                EndOperation(operationName, true);
            }
            catch (Exception ex)
            {
                EndOperation(operationName, false, ex.Message);
                throw;
            }
        }

        /// <summary>
        /// Gets the current performance report
        /// </summary>
        public PerformanceReport GetReport()
        {
            return new PerformanceReport
            {
                Metrics = new List<PerformanceMetric>(_metrics)
            };
        }

        /// <summary>
        /// Clears all performance metrics
        /// </summary>
        public void Clear()
        {
            _metrics.Clear();
            _activeOperations.Clear();
            _operationMemoryStart.Clear();
        }

        /// <summary>
        /// Gets performance statistics for a specific operation
        /// </summary>
        public OperationStatistics GetOperationStatistics(string operationName)
        {
            var operationMetrics = _metrics.Where(m => m.OperationName == operationName).ToList();

            if (!operationMetrics.Any())
                return null;

            return new OperationStatistics
            {
                OperationName = operationName,
                TotalExecutions = operationMetrics.Count,
                SuccessfulExecutions = operationMetrics.Count(m => m.IsSuccess),
                FailedExecutions = operationMetrics.Count(m => !m.IsSuccess),
                AverageDuration = TimeSpan.FromMilliseconds(operationMetrics.Average(m => m.Duration.TotalMilliseconds)),
                MinDuration = operationMetrics.Min(m => m.Duration),
                MaxDuration = operationMetrics.Max(m => m.Duration),
                AverageMemoryUsage = (long)operationMetrics.Average(m => m.MemoryUsed),
                TotalMemoryUsage = operationMetrics.Sum(m => m.MemoryUsed)
            };
        }

        /// <summary>
        /// Prints a detailed performance report
        /// </summary>
        public void PrintReport()
        {
            var report = GetReport();

            Debug.WriteLine("=== PowerBIM Performance Report ===");
            Debug.WriteLine($"Report Time: {report.ReportTime:yyyy-MM-dd HH:mm:ss}");
            Debug.WriteLine($"Total Operations: {report.Metrics.Count}");
            Debug.WriteLine($"Successful: {report.SuccessfulOperations}, Failed: {report.FailedOperations}");
            Debug.WriteLine($"Total Duration: {report.TotalDuration.TotalSeconds:F2} seconds");
            Debug.WriteLine($"Total Memory Used: {FormatBytes(report.TotalMemoryUsed)}");

            // Group by operation name
            var groupedMetrics = report.Metrics.GroupBy(m => m.OperationName);

            foreach (var group in groupedMetrics.OrderBy(g => g.Key))
            {
                var stats = GetOperationStatistics(group.Key);
                Debug.WriteLine($"Operation: {group.Key}");
                Debug.WriteLine($"  Executions: {stats.TotalExecutions} (Success: {stats.SuccessfulExecutions}, Failed: {stats.FailedExecutions})");
                Debug.WriteLine($"  Duration: Avg={stats.AverageDuration.TotalMilliseconds:F2}ms, Min={stats.MinDuration.TotalMilliseconds:F2}ms, Max={stats.MaxDuration.TotalMilliseconds:F2}ms");
                Debug.WriteLine($"  Memory: Avg={FormatBytes(stats.AverageMemoryUsage)}, Total={FormatBytes(stats.TotalMemoryUsage)}");

            }
        }

        #endregion

        #region Helper Methods

        private static string FormatBytes(long bytes)
        {
            if (bytes == 0) return "0 B";

            string[] sizes = { "B", "KB", "MB", "GB" };
            int order = 0;
            double size = bytes;

            while (size >= 1024 && order < sizes.Length - 1)
            {
                order++;
                size /= 1024;
            }

            return $"{size:F2} {sizes[order]}";
        }

        #endregion

        #region Nested Classes

        public class OperationStatistics
        {
            public string OperationName { get; set; }
            public int TotalExecutions { get; set; }
            public int SuccessfulExecutions { get; set; }
            public int FailedExecutions { get; set; }
            public TimeSpan AverageDuration { get; set; }
            public TimeSpan MinDuration { get; set; }
            public TimeSpan MaxDuration { get; set; }
            public long AverageMemoryUsage { get; set; }
            public long TotalMemoryUsage { get; set; }
            public double SuccessRate => TotalExecutions > 0 ? (double)SuccessfulExecutions / TotalExecutions * 100 : 0;
        }

        #endregion

        #region Static Instance

        private static readonly Lazy<PowerBIMPerformanceMonitor> _instance =
            new Lazy<PowerBIMPerformanceMonitor>(() => new PowerBIMPerformanceMonitor());

        public static PowerBIMPerformanceMonitor Instance => _instance.Value;

        #endregion
    }
}
