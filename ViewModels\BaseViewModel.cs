﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using CommunityToolkit.Mvvm.ComponentModel;

namespace MEP.PowerBIM_6.ViewModels
{
    /// <summary>
    /// Base ViewModel class that implements INotifyPropertyChanged
    /// </summary>
    public abstract class BaseViewModel : ObservableObject
    {
        private bool _isBusy;
        private string _statusMessage = "Ready";

        /// <summary>
        /// Gets or sets a value indicating whether the ViewModel is busy
        /// </summary>
        public bool IsBusy
        {
            get => _isBusy;
            set => SetProperty(ref _isBusy, value);
        }

        /// <summary>
        /// Gets or sets the status message
        /// </summary>
        public string StatusMessage
        {
            get => _statusMessage;
            set => SetProperty(ref _statusMessage, value);
        }

        /// <summary>
        /// Sets the busy state and status message
        /// </summary>
        /// <param name="isBusy">Whether the ViewModel is busy</param>
        /// <param name="message">Status message to display</param>
        protected void SetBusyState(bool isBusy, string message = "")
        {
            IsBusy = isBusy;
            StatusMessage = string.IsNullOrEmpty(message) ? (isBusy ? "Processing..." : "Ready") : message;
        }

        /// <summary>
        /// Called when the associated view is closing
        /// </summary>
        public virtual void OnViewClosing()
        {
            // Override in derived classes if needed
        }
    }
}
