﻿using System.Globalization;
using System.Windows.Data;
using Visibility = System.Windows.Visibility;

namespace MEP.PowerBIM_6.Converters
{
    public class BooleanToVisibilityConverter : IValueConverter
    {
        /// <summary>
        /// Converts boolean values to Visibility
        /// </summary>
        public bool Invert { get; set; } = false;

        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool boolValue)
            {
                bool result = Invert ? !boolValue : boolValue;
                return result ? Visibility.Visible : Visibility.Collapsed;
            }

            return Visibility.Collapsed;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is Visibility visibility)
            {
                bool result = visibility == Visibility.Visible;
                return Invert ? !result : result;
            }

            return false;
        }
    }

    /// <summary>
    /// Converts null or empty strings to Visibility
    /// </summary>
    public class StringToVisibilityConverter : IValueConverter
    {
        public bool ShowWhenEmpty { get; set; } = false;

        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            bool isEmpty = string.IsNullOrEmpty(value as string);
            bool shouldShow = ShowWhenEmpty ? isEmpty : !isEmpty;
            return shouldShow ? Visibility.Visible : Visibility.Collapsed;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    /// <summary>
    /// Converts numeric values to formatted strings
    /// </summary>
    public class NumericFormatConverter : IValueConverter
    {
        public string Format { get; set; } = "F2";
        public string Unit { get; set; } = string.Empty;

        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is double doubleValue)
            {
                var formatted = doubleValue.ToString(Format, culture);
                return string.IsNullOrEmpty(Unit) ? formatted : $"{formatted} {Unit}";
            }

            if (value is int intValue)
            {
                var formatted = intValue.ToString(culture);
                return string.IsNullOrEmpty(Unit) ? formatted : $"{formatted} {Unit}";
            }

            return value?.ToString() ?? string.Empty;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is string stringValue)
            {
                // Remove unit if present
                if (!string.IsNullOrEmpty(Unit) && stringValue.EndsWith(Unit))
                {
                    stringValue = stringValue.Substring(0, stringValue.Length - Unit.Length).Trim();
                }

                if (targetType == typeof(double) || targetType == typeof(double?))
                {
                    if (double.TryParse(stringValue, NumberStyles.Float, culture, out double doubleResult))
                        return doubleResult;
                }

                if (targetType == typeof(int) || targetType == typeof(int?))
                {
                    if (int.TryParse(stringValue, NumberStyles.Integer, culture, out int intResult))
                        return intResult;
                }
            }

            return value;
        }
    }

    /// <summary>
    /// Converts percentage values (0-1) to display format (0-100%)
    /// </summary>
    public class PercentageConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is double doubleValue)
            {
                return $"{(doubleValue * 100):F1}%";
            }

            return "0%";
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is string stringValue)
            {
                stringValue = stringValue.Replace("%", "").Trim();
                if (double.TryParse(stringValue, NumberStyles.Float, culture, out double result))
                {
                    return result / 100.0;
                }
            }

            return 0.0;
        }
    }

    /// <summary>
    /// Inverts boolean values
    /// </summary>
    public class InverseBooleanConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool boolValue)
                return !boolValue;

            return true;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool boolValue)
                return !boolValue;

            return false;
        }
    }

    /// <summary>
    /// Converts multiple boolean values using AND logic
    /// </summary>
    public class MultiBooleanAndConverter : IMultiValueConverter
    {
        public object Convert(object[] values, Type targetType, object parameter, CultureInfo culture)
        {
            if (values == null || values.Length == 0)
                return false;

            foreach (var value in values)
            {
                if (!(value is bool boolValue) || !boolValue)
                    return false;
            }

            return true;
        }

        public object[] ConvertBack(object value, Type[] targetTypes, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    /// <summary>
    /// Converts multiple boolean values using OR logic
    /// </summary>
    public class MultiBooleanOrConverter : IMultiValueConverter
    {
        public object Convert(object[] values, Type targetType, object parameter, CultureInfo culture)
        {
            if (values == null || values.Length == 0)
                return false;

            foreach (var value in values)
            {
                if (value is bool boolValue && boolValue)
                    return true;
            }

            return false;
        }

        public object[] ConvertBack(object value, Type[] targetTypes, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
