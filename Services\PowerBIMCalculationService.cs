﻿using Autodesk.Revit.UI;
using BecaActivityLogger.CoreLogic.Data;
using MEP.PowerBIM_6.Models;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MEP.PowerBIM_6.Services
{
    /// <summary>
    /// Service for PowerBIM electrical calculations
    /// </summary>
    public class PowerBIMCalculationService
    {
        private readonly UIApplication _uiApplication;
        private readonly BecaActivityLoggerData _logger;

        public PowerBIMCalculationService(UIApplication uiApplication, BecaActivityLoggerData logger)
        {
            _uiApplication = uiApplication;
            _logger = logger;
        }

        /// <summary>
        /// Runs the auto sizer for all selected distribution boards
        /// </summary>
        public async Task<bool> RunAutoSizerAsync(
            PowerBIMProjectInfoModel projectInfo,
            ObservableCollection<PowerBIMDBDataModelEnhanced> distributionBoards,
            IProgress<string> progress = null)
        {
            try
            {
                progress?.Report("Starting auto sizer...");
                _logger?.PostTaskEnd("Auto Sizer Started");

                var selectedDBs = distributionBoards.Where(db => db.IsSelected).ToList();
                if (!selectedDBs.Any())
                {
                    progress?.Report("No distribution boards selected");
                    return false;
                }

                int totalDBs = selectedDBs.Count;
                int processedDBs = 0;

                foreach (var db in selectedDBs)
                {
                    progress?.Report($"Processing {db.Schedule_DB_Name} ({processedDBs + 1}/{totalDBs})...");

                    // Process each circuit in the DB
                    await ProcessDistributionBoardAsync(db, projectInfo);

                    processedDBs++;
                    progress?.Report($"Completed {db.Schedule_DB_Name} ({processedDBs}/{totalDBs})");
                }

                progress?.Report("Auto sizer completed successfully");
                _logger?.PostTaskEnd("Auto Sizer Completed Successfully");
                return true;
            }
            catch (Exception ex)
            {
                var errorMsg = $"Auto sizer failed: {ex.Message}";
                progress?.Report(errorMsg);
                _logger?.PostTaskEnd(errorMsg);
                return false;
            }
        }

        /// <summary>
        /// Processes a single distribution board
        /// </summary>
        private async Task ProcessDistributionBoardAsync(PowerBIMDBDataModelEnhanced db, PowerBIMProjectInfoModel projectInfo)
        {
            try
            {
                // Reset counters
                db.Result_PassCount = 0;
                db.Result_WarningCount = 0;
                db.Result_FailCount = 0;

                // Process each circuit
                foreach (var circuit in db.Circuits)
                {
                    await ProcessCircuitAsync(circuit, db, projectInfo);
                }

                // Update DB status
                db.UpdateCircuitCounts();
                db.DB_All_Circuits_Pass = db.Result_FailCount == 0;

                // Generate summary
                db.DB_Result_Summary = GenerateDBSummary(db);

                // Simulate processing delay
                await Task.Delay(100);
            }
            catch (Exception ex)
            {
                db.GUI_Notes = $"Error processing DB: {ex.Message}";
                db.Data_Good = false;
            }
        }

        /// <summary>
        /// Processes a single circuit
        /// </summary>
        private async Task ProcessCircuitAsync(PowerBIMCircuitDataModel circuit, PowerBIMDBDataModelEnhanced db, PowerBIMProjectInfoModel projectInfo)
        {
            try
            {
                // Skip spare/space circuits
                if (circuit.CCT_Is_Spare_Or_Space)
                {
                    circuit.CheckResult = "Pass";
                    return;
                }

                // Validate circuit data
                if (!ValidateCircuitData(circuit))
                {
                    circuit.CheckResult = "Fail";
                    circuit.ErrorMessage = "Invalid circuit data";
                    return;
                }

                // Perform cable sizing calculations
                var cableResult = await CalculateCableSizingAsync(circuit, projectInfo);

                // Perform breaker calculations
                var breakerResult = await CalculateBreakerSizingAsync(circuit, projectInfo);

                // Perform voltage drop calculations
                var voltageDropResult = await CalculateVoltageDropAsync(circuit, db, projectInfo);

                // Determine overall result
                circuit.CheckResult = DetermineCircuitResult(cableResult, breakerResult, voltageDropResult);

                // Simulate processing delay
                await Task.Delay(10);
            }
            catch (Exception ex)
            {
                circuit.CheckResult = "Fail";
                circuit.ErrorMessage = $"Calculation error: {ex.Message}";
            }
        }

        /// <summary>
        /// Validates circuit data
        /// </summary>
        private bool ValidateCircuitData(PowerBIMCircuitDataModel circuit)
        {
            if (string.IsNullOrEmpty(circuit.CCT_Number))
                return false;

            if (circuit.EffectiveCurrent <= 0)
                return false;

            if (circuit.Number_Of_Poles <= 0 || circuit.Number_Of_Poles > 3)
                return false;

            return true;
        }

        /// <summary>
        /// Calculates cable sizing
        /// </summary>
        private async Task<string> CalculateCableSizingAsync(PowerBIMCircuitDataModel circuit, PowerBIMProjectInfoModel projectInfo)
        {
            // TODO: Implement actual cable sizing logic
            await Task.Delay(5);

            // Placeholder logic
            if (circuit.EffectiveCurrent > 100)
                return "Warning"; // Large current

            return "Pass";
        }

        /// <summary>
        /// Calculates breaker sizing
        /// </summary>
        private async Task<string> CalculateBreakerSizingAsync(PowerBIMCircuitDataModel circuit, PowerBIMProjectInfoModel projectInfo)
        {
            // TODO: Implement actual breaker sizing logic
            await Task.Delay(5);

            // Placeholder logic
            if (circuit.Schedule_Trip_Rating <= 0)
                return "Fail"; // No breaker specified

            if (circuit.Schedule_Trip_Rating < circuit.EffectiveCurrent)
                return "Warning"; // Undersized breaker

            return "Pass";
        }

        /// <summary>
        /// Calculates voltage drop
        /// </summary>
        private async Task<string> CalculateVoltageDropAsync(PowerBIMCircuitDataModel circuit, PowerBIMDBDataModelEnhanced db, PowerBIMProjectInfoModel projectInfo)
        {
            // TODO: Implement actual voltage drop calculation
            await Task.Delay(5);

            // Placeholder logic
            var maxVD = projectInfo.SystemVDMaxPerc;
            var calculatedVD = 0.03; // Placeholder 3% VD

            if (calculatedVD > maxVD)
                return "Fail"; // Voltage drop too high

            if (calculatedVD > maxVD * 0.8)
                return "Warning"; // Close to limit

            return "Pass";
        }

        /// <summary>
        /// Determines the overall circuit result
        /// </summary>
        private string DetermineCircuitResult(string cableResult, string breakerResult, string voltageDropResult)
        {
            var results = new[] { cableResult, breakerResult, voltageDropResult };

            if (results.Any(r => r == "Fail"))
                return "Fail";

            if (results.Any(r => r == "Warning"))
                return "Warning";

            return "Pass";
        }

        /// <summary>
        /// Generates a summary for the distribution board
        /// </summary>
        private string GenerateDBSummary(PowerBIMDBDataModelEnhanced db)
        {
            var total = db.TotalCircuitCount;
            var pass = db.Result_PassCount;
            var warning = db.Result_WarningCount;
            var fail = db.Result_FailCount;

            return $"Total: {total}, Pass: {pass}, Warning: {warning}, Fail: {fail}";
        }

        /// <summary>
        /// Loads project data from Revit
        /// </summary>
        public async Task<bool> LoadProjectDataAsync(
            PowerBIMProjectInfoModel projectInfo,
            ObservableCollection<PowerBIMDBDataModelEnhanced> distributionBoards,
            IProgress<string> progress = null)
        {
            try
            {
                progress?.Report("Loading project data...");
                _logger?.PostTaskEnd("Loading Project Data");

                // TODO: Implement actual Revit data loading
                await Task.Delay(1000);

                // Placeholder: Create sample data
                CreateSampleData(distributionBoards);

                progress?.Report("Project data loaded successfully");
                _logger?.PostTaskEnd("Project Data Loaded Successfully");
                return true;
            }
            catch (Exception ex)
            {
                var errorMsg = $"Failed to load project data: {ex.Message}";
                progress?.Report(errorMsg);
                _logger?.PostTaskEnd(errorMsg);
                return false;
            }
        }

        /// <summary>
        /// Creates sample data for testing
        /// </summary>
        private void CreateSampleData(ObservableCollection<PowerBIMDBDataModelEnhanced> distributionBoards)
        {
            distributionBoards.Clear();

            // Sample DB 1
            var db1 = new PowerBIMDBDataModelEnhanced
            {
                Schedule_DB_Name = "DB-01",
                Result_PassCount = 8,
                Result_WarningCount = 2,
                Result_FailCount = 1,
                GUI_Notes = "Sample distribution board",
                Data_Good = true
            };
            distributionBoards.Add(db1);

            // Sample DB 2
            var db2 = new PowerBIMDBDataModelEnhanced
            {
                Schedule_DB_Name = "DB-02",
                Result_PassCount = 12,
                Result_WarningCount = 0,
                Result_FailCount = 0,
                GUI_Notes = "All circuits pass",
                Data_Good = true
            };
            distributionBoards.Add(db2);

            // Sample DB 3
            var db3 = new PowerBIMDBDataModelEnhanced
            {
                Schedule_DB_Name = "DB-03",
                Result_PassCount = 5,
                Result_WarningCount = 3,
                Result_FailCount = 2,
                GUI_Notes = "Requires attention",
                Data_Good = true
            };
            distributionBoards.Add(db3);
        }
    }
}
