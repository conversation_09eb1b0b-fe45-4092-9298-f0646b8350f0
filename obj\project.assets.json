{"version": 3, "targets": {".NETFramework,Version=v4.8": {"CommunityToolkit.Mvvm/8.4.0": {"type": "package", "dependencies": {"Microsoft.Bcl.AsyncInterfaces": "8.0.0", "System.ComponentModel.Annotations": "5.0.0", "System.Memory": "4.6.0", "System.Runtime.CompilerServices.Unsafe": "6.1.0"}, "compile": {"lib/netstandard2.0/CommunityToolkit.Mvvm.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/CommunityToolkit.Mvvm.dll": {"related": ".pdb;.xml"}}, "build": {"buildTransitive/CommunityToolkit.Mvvm.targets": {}}}, "DocumentFormat.OpenXml/3.0.1": {"type": "package", "dependencies": {"DocumentFormat.OpenXml.Framework": "3.0.1"}, "frameworkAssemblies": ["System", "System.Xml", "WindowsBase"], "compile": {"lib/net46/DocumentFormat.OpenXml.dll": {"related": ".xml"}}, "runtime": {"lib/net46/DocumentFormat.OpenXml.dll": {"related": ".xml"}}}, "DocumentFormat.OpenXml.Framework/3.0.1": {"type": "package", "frameworkAssemblies": ["System", "System.Xml", "WindowsBase"], "compile": {"lib/net46/DocumentFormat.OpenXml.Framework.dll": {"related": ".xml"}}, "runtime": {"lib/net46/DocumentFormat.OpenXml.Framework.dll": {"related": ".xml"}}}, "JetBrains.Annotations/2024.3.0": {"type": "package", "compile": {"lib/net20/JetBrains.Annotations.dll": {"related": ".xml"}}, "runtime": {"lib/net20/JetBrains.Annotations.dll": {"related": ".xml"}}}, "MaterialDesignColors/5.2.1": {"type": "package", "compile": {"lib/net462/MaterialDesignColors.dll": {"related": ".pdb"}}, "runtime": {"lib/net462/MaterialDesignColors.dll": {"related": ".pdb"}}}, "MaterialDesignThemes/5.2.1": {"type": "package", "dependencies": {"MaterialDesignColors": "5.2.1", "Microsoft.Xaml.Behaviors.Wpf": "1.1.39"}, "compile": {"lib/net462/MaterialDesignThemes.Wpf.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net462/MaterialDesignThemes.Wpf.dll": {"related": ".pdb;.xml"}}, "build": {"build/MaterialDesignThemes.targets": {}}}, "Microsoft.Bcl.AsyncInterfaces/8.0.0": {"type": "package", "dependencies": {"System.Threading.Tasks.Extensions": "4.5.4"}, "compile": {"lib/net462/Microsoft.Bcl.AsyncInterfaces.dll": {"related": ".xml"}}, "runtime": {"lib/net462/Microsoft.Bcl.AsyncInterfaces.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "Microsoft.Office.Interop.Excel/15.0.4795.1001": {"type": "package", "compile": {"lib/net20/Microsoft.Office.Interop.Excel.dll": {}}, "runtime": {"lib/net20/Microsoft.Office.Interop.Excel.dll": {}}}, "Microsoft.Xaml.Behaviors.Wpf/1.1.39": {"type": "package", "frameworkAssemblies": ["PresentationCore", "PresentationFramework", "System", "System.Core", "System.XML", "System.Xaml", "WindowsBase"], "compile": {"lib/net45/Microsoft.Xaml.Behaviors.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net45/Microsoft.Xaml.Behaviors.dll": {"related": ".pdb;.xml"}}}, "Nice3point.Revit.Api.RevitAPI/2024.3.10": {"type": "package", "compile": {"ref/net48/RevitAPI.dll": {"related": ".xml"}}}, "Nice3point.Revit.Api.RevitAPIUI/2024.3.10": {"type": "package", "compile": {"ref/net48/RevitAPIUI.dll": {"related": ".xml"}}}, "Nice3point.Revit.Build.Tasks/2.0.2": {"type": "package", "build": {"build/Nice3point.Revit.Build.Tasks.props": {}, "build/Nice3point.Revit.Build.Tasks.targets": {}}}, "Nice3point.Revit.Extensions/2024.2.1": {"type": "package", "dependencies": {"JetBrains.Annotations": "2024.3.0"}, "compile": {"lib/net48/Nice3point.Revit.Extensions.dll": {"related": ".xml"}}, "runtime": {"lib/net48/Nice3point.Revit.Extensions.dll": {"related": ".xml"}}}, "Nice3point.Revit.Toolkit/2024.2.0": {"type": "package", "compile": {"lib/net48/Nice3point.Revit.Toolkit.dll": {"related": ".xml"}}, "runtime": {"lib/net48/Nice3point.Revit.Toolkit.dll": {"related": ".xml"}}}, "System.Buffers/4.6.0": {"type": "package", "compile": {"lib/net462/System.Buffers.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.Buffers.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "System.ComponentModel.Annotations/5.0.0": {"type": "package", "frameworkAssemblies": ["System.ComponentModel.DataAnnotations", "mscorlib"], "compile": {"ref/net461/System.ComponentModel.Annotations.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.ComponentModel.Annotations.dll": {}}}, "System.Memory/4.6.0": {"type": "package", "dependencies": {"System.Buffers": "4.6.0", "System.Numerics.Vectors": "4.6.0", "System.Runtime.CompilerServices.Unsafe": "6.1.0"}, "compile": {"lib/net462/System.Memory.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.Memory.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "System.Numerics.Vectors/4.6.0": {"type": "package", "frameworkAssemblies": ["System.Numerics"], "compile": {"lib/net462/System.Numerics.Vectors.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.Numerics.Vectors.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "System.Resources.Extensions/8.0.0": {"type": "package", "dependencies": {"System.Memory": "4.5.5"}, "compile": {"lib/net462/System.Resources.Extensions.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.Resources.Extensions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/System.Resources.Extensions.targets": {}}}, "System.Runtime.CompilerServices.Unsafe/6.1.0": {"type": "package", "frameworkAssemblies": ["mscorlib"], "compile": {"lib/net462/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "System.Threading.Tasks.Extensions/4.5.4": {"type": "package", "dependencies": {"System.Runtime.CompilerServices.Unsafe": "4.5.3"}, "frameworkAssemblies": ["mscorlib"], "compile": {"lib/net461/System.Threading.Tasks.Extensions.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Threading.Tasks.Extensions.dll": {"related": ".xml"}}}}}, "libraries": {"CommunityToolkit.Mvvm/8.4.0": {"sha512": "tqVU8yc/ADO9oiTRyTnwhFN68hCwvkliMierptWOudIAvWY1mWCh5VFh+guwHJmpMwfg0J0rY+yyd5Oy7ty9Uw==", "type": "package", "path": "communitytoolkit.mvvm/8.4.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "License.md", "ThirdPartyNotices.txt", "analyzers/dotnet/roslyn4.0/cs/CommunityToolkit.Mvvm.CodeFixers.dll", "analyzers/dotnet/roslyn4.0/cs/CommunityToolkit.Mvvm.SourceGenerators.dll", "analyzers/dotnet/roslyn4.12/cs/CommunityToolkit.Mvvm.CodeFixers.dll", "analyzers/dotnet/roslyn4.12/cs/CommunityToolkit.Mvvm.SourceGenerators.dll", "analyzers/dotnet/roslyn4.3/cs/CommunityToolkit.Mvvm.CodeFixers.dll", "analyzers/dotnet/roslyn4.3/cs/CommunityToolkit.Mvvm.SourceGenerators.dll", "build/CommunityToolkit.Mvvm.FeatureSwitches.targets", "build/CommunityToolkit.Mvvm.SourceGenerators.targets", "build/CommunityToolkit.Mvvm.Windows.targets", "build/CommunityToolkit.Mvvm.WindowsSdk.targets", "build/CommunityToolkit.Mvvm.targets", "buildTransitive/CommunityToolkit.Mvvm.FeatureSwitches.targets", "buildTransitive/CommunityToolkit.Mvvm.SourceGenerators.targets", "buildTransitive/CommunityToolkit.Mvvm.Windows.targets", "buildTransitive/CommunityToolkit.Mvvm.WindowsSdk.targets", "buildTransitive/CommunityToolkit.Mvvm.targets", "communitytoolkit.mvvm.8.4.0.nupkg.sha512", "communitytoolkit.mvvm.nuspec", "lib/net8.0-windows10.0.17763/CommunityToolkit.Mvvm.dll", "lib/net8.0-windows10.0.17763/CommunityToolkit.Mvvm.pdb", "lib/net8.0-windows10.0.17763/CommunityToolkit.Mvvm.xml", "lib/net8.0/CommunityToolkit.Mvvm.dll", "lib/net8.0/CommunityToolkit.Mvvm.pdb", "lib/net8.0/CommunityToolkit.Mvvm.xml", "lib/netstandard2.0/CommunityToolkit.Mvvm.dll", "lib/netstandard2.0/CommunityToolkit.Mvvm.pdb", "lib/netstandard2.0/CommunityToolkit.Mvvm.xml", "lib/netstandard2.1/CommunityToolkit.Mvvm.dll", "lib/netstandard2.1/CommunityToolkit.Mvvm.pdb", "lib/netstandard2.1/CommunityToolkit.Mvvm.xml"]}, "DocumentFormat.OpenXml/3.0.1": {"sha512": "DCK1cwFUJ1FGGyYyo++HWl9H1RkqMWIu+FGOLRy6E4L4y0/HIhlJ7N/n1HKboFfOwKn1cMBRxt1RCuDbIEy5YQ==", "type": "package", "path": "documentformat.openxml/3.0.1", "files": [".nupkg.metadata", ".signature.p7s", "documentformat.openxml.3.0.1.nupkg.sha512", "documentformat.openxml.nuspec", "icon.png", "lib/net35/DocumentFormat.OpenXml.dll", "lib/net35/DocumentFormat.OpenXml.xml", "lib/net40/DocumentFormat.OpenXml.dll", "lib/net40/DocumentFormat.OpenXml.xml", "lib/net46/DocumentFormat.OpenXml.dll", "lib/net46/DocumentFormat.OpenXml.xml", "lib/net8.0/DocumentFormat.OpenXml.dll", "lib/net8.0/DocumentFormat.OpenXml.xml", "lib/netstandard2.0/DocumentFormat.OpenXml.dll", "lib/netstandard2.0/DocumentFormat.OpenXml.xml"]}, "DocumentFormat.OpenXml.Framework/3.0.1": {"sha512": "ifyI7OW7sggz7LQMIAD2aUsY/zVUON9QaHrpZ4MK33iVMeHlTG4uhUE2aLWb31nry+LCs2ALDAwf8OfUJGjgBg==", "type": "package", "path": "documentformat.openxml.framework/3.0.1", "files": [".nupkg.metadata", ".signature.p7s", "documentformat.openxml.framework.3.0.1.nupkg.sha512", "documentformat.openxml.framework.nuspec", "icon.png", "lib/net35/DocumentFormat.OpenXml.Framework.dll", "lib/net35/DocumentFormat.OpenXml.Framework.xml", "lib/net40/DocumentFormat.OpenXml.Framework.dll", "lib/net40/DocumentFormat.OpenXml.Framework.xml", "lib/net46/DocumentFormat.OpenXml.Framework.dll", "lib/net46/DocumentFormat.OpenXml.Framework.xml", "lib/net6.0/DocumentFormat.OpenXml.Framework.dll", "lib/net6.0/DocumentFormat.OpenXml.Framework.xml", "lib/net8.0/DocumentFormat.OpenXml.Framework.dll", "lib/net8.0/DocumentFormat.OpenXml.Framework.xml", "lib/netstandard2.0/DocumentFormat.OpenXml.Framework.dll", "lib/netstandard2.0/DocumentFormat.OpenXml.Framework.xml"]}, "JetBrains.Annotations/2024.3.0": {"sha512": "ox5pkeLQXjvJdyAB4b2sBYAlqZGLh3PjSnP1bQNVx72ONuTJ9+34/+Rq91Fc0dG29XG9RgZur9+NcP4riihTug==", "type": "package", "path": "jetbrains.annotations/2024.3.0", "files": [".nupkg.metadata", ".signature.p7s", "icon.png", "jetbrains.annotations.2024.3.0.nupkg.sha512", "jetbrains.annotations.nuspec", "lib/net20/JetBrains.Annotations.dll", "lib/net20/JetBrains.Annotations.xml", "lib/netstandard1.0/JetBrains.Annotations.deps.json", "lib/netstandard1.0/JetBrains.Annotations.dll", "lib/netstandard1.0/JetBrains.Annotations.xml", "lib/netstandard2.0/JetBrains.Annotations.deps.json", "lib/netstandard2.0/JetBrains.Annotations.dll", "lib/netstandard2.0/JetBrains.Annotations.xml", "lib/portable40-net40+sl5+win8+wp8+wpa81/JetBrains.Annotations.dll", "lib/portable40-net40+sl5+win8+wp8+wpa81/JetBrains.Annotations.xml", "readme.md"]}, "MaterialDesignColors/5.2.1": {"sha512": "D0HW6E2/kzsnEWCh1KDG/K09Fpkvs9mR3n91Y8YSOsEAoQmGZbVAj58ssyAxGTiIPj2zB4ZVnwxkizwO35/v8A==", "type": "package", "path": "materialdesigncolors/5.2.1", "files": [".nupkg.metadata", ".signature.p7s", "docs/README.md", "images/MaterialDesignColors.Icon.png", "lib/net462/MaterialDesignColors.dll", "lib/net462/MaterialDesignColors.pdb", "lib/net6.0/MaterialDesignColors.dll", "lib/net6.0/MaterialDesignColors.pdb", "lib/net8.0/MaterialDesignColors.dll", "lib/net8.0/MaterialDesignColors.pdb", "materialdesigncolors.5.2.1.nupkg.sha512", "materialdesigncolors.nuspec"]}, "MaterialDesignThemes/5.2.1": {"sha512": "x8JDqNHJcTLLxIoVts3w7AbSq5Zo0FXTw89XqPN7+n0EKqLXFwWsywiUn08HDyTGAmZVJqbQsWKxKWCI8qfWsQ==", "type": "package", "path": "materialdesignthemes/5.2.1", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "build/MaterialDesignThemes.targets", "build/Resources/Roboto/Roboto-Black.ttf", "build/Resources/Roboto/Roboto-BlackItalic.ttf", "build/Resources/Roboto/Roboto-Bold.ttf", "build/Resources/Roboto/Roboto-BoldItalic.ttf", "build/Resources/Roboto/Roboto-Italic.ttf", "build/Resources/Roboto/Roboto-Light.ttf", "build/Resources/Roboto/Roboto-LightItalic.ttf", "build/Resources/Roboto/Roboto-Medium.ttf", "build/Resources/Roboto/Roboto-MediumItalic.ttf", "build/Resources/Roboto/Roboto-Regular.ttf", "build/Resources/Roboto/Roboto-Thin.ttf", "build/Resources/Roboto/Roboto-ThinItalic.ttf", "build/Resources/Roboto/RobotoCondensed-Bold.ttf", "build/Resources/Roboto/RobotoCondensed-BoldItalic.ttf", "build/Resources/Roboto/RobotoCondensed-Italic.ttf", "build/Resources/Roboto/RobotoCondensed-Light.ttf", "build/Resources/Roboto/RobotoCondensed-LightItalic.ttf", "build/Resources/Roboto/RobotoCondensed-Regular.ttf", "docs/README.md", "images/MaterialDesignThemes.Icon.png", "lib/net462/MaterialDesignThemes.Wpf.dll", "lib/net462/MaterialDesignThemes.Wpf.pdb", "lib/net462/MaterialDesignThemes.Wpf.xml", "lib/net6.0/MaterialDesignThemes.Wpf.dll", "lib/net6.0/MaterialDesignThemes.Wpf.pdb", "lib/net6.0/MaterialDesignThemes.Wpf.xml", "lib/net8.0/MaterialDesignThemes.Wpf.dll", "lib/net8.0/MaterialDesignThemes.Wpf.pdb", "lib/net8.0/MaterialDesignThemes.Wpf.xml", "materialdesignthemes.5.2.1.nupkg.sha512", "materialdesignthemes.nuspec", "tools/VisualStudioToolsManifest.xml"]}, "Microsoft.Bcl.AsyncInterfaces/8.0.0": {"sha512": "3WA9q9yVqJp222P3x1wYIGDAkpjAku0TMUaaQV22g6L67AI0LdOIrVS7Ht2vJfLHGSPVuqN94vIr15qn+HEkHw==", "type": "package", "path": "microsoft.bcl.asyncinterfaces/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Bcl.AsyncInterfaces.targets", "buildTransitive/net462/_._", "lib/net462/Microsoft.Bcl.AsyncInterfaces.dll", "lib/net462/Microsoft.Bcl.AsyncInterfaces.xml", "lib/netstandard2.0/Microsoft.Bcl.AsyncInterfaces.dll", "lib/netstandard2.0/Microsoft.Bcl.AsyncInterfaces.xml", "lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll", "lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.xml", "microsoft.bcl.asyncinterfaces.8.0.0.nupkg.sha512", "microsoft.bcl.asyncinterfaces.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Office.Interop.Excel/15.0.4795.1001": {"sha512": "cuvqi/U5MYSM0gvR2l90q0m/urRgmg69EiwP5VWp1RcaJ0YT5G26Va5LaOZ3KJFc22FNihS5CUjeePUp2YpGQA==", "type": "package", "path": "microsoft.office.interop.excel/15.0.4795.1001", "files": [".nupkg.metadata", ".signature.p7s", "lib/net20/Microsoft.Office.Interop.Excel.dll", "lib/netstandard2.0/Microsoft.Office.Interop.Excel.dll", "microsoft.office.interop.excel.15.0.4795.1001.nupkg.sha512", "microsoft.office.interop.excel.nuspec"]}, "Microsoft.Xaml.Behaviors.Wpf/1.1.39": {"sha512": "8PZKqw9QOcu42xk8puY4P1+EXHL9YGOR9b7qhaYx5cILHul456H073tj99vyPcCt0W0781T9RwHqkx507ZyUpQ==", "type": "package", "path": "microsoft.xaml.behaviors.wpf/1.1.39", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "lib/net45/Design/Microsoft.Xaml.Behaviors.Design.dll", "lib/net45/Microsoft.Xaml.Behaviors.dll", "lib/net45/Microsoft.Xaml.Behaviors.pdb", "lib/net45/Microsoft.Xaml.Behaviors.xml", "lib/net5.0-windows7.0/Design/Microsoft.Xaml.Behaviors.DesignTools.dll", "lib/net5.0-windows7.0/Microsoft.Xaml.Behaviors.dll", "lib/net5.0-windows7.0/Microsoft.Xaml.Behaviors.pdb", "lib/net5.0-windows7.0/Microsoft.Xaml.Behaviors.xml", "lib/netcoreapp3.1/Design/Microsoft.Xaml.Behaviors.DesignTools.dll", "lib/netcoreapp3.1/Microsoft.Xaml.Behaviors.dll", "lib/netcoreapp3.1/Microsoft.Xaml.Behaviors.pdb", "lib/netcoreapp3.1/Microsoft.Xaml.Behaviors.xml", "microsoft.xaml.behaviors.wpf.1.1.39.nupkg.sha512", "microsoft.xaml.behaviors.wpf.nuspec", "tools/Install.ps1"]}, "Nice3point.Revit.Api.RevitAPI/2024.3.10": {"sha512": "7rewl3UT97UxO/e42GjlbxcBHXobGsy859XSQHweMQJJ9STuPQl51LtBpV1J+V6YiAPGbtGKdLigb1bJiDGlNQ==", "type": "package", "path": "nice3point.revit.api.revitapi/2024.3.10", "files": [".nupkg.metadata", ".signature.p7s", "License.md", "Readme.md", "images/PackageIcon.png", "nice3point.revit.api.revitapi.2024.3.10.nupkg.sha512", "nice3point.revit.api.revitapi.nuspec", "ref/net48/RevitAPI.dll", "ref/net48/RevitAPI.xml"]}, "Nice3point.Revit.Api.RevitAPIUI/2024.3.10": {"sha512": "ONzmxUh80HGkzE2y1FMOanDz5zfGye+vTMc5AQVQgW/Fk9WAozefhNFJB6zYGLGQ8eaeFKyK0oyjPiF1ClOewA==", "type": "package", "path": "nice3point.revit.api.revitapiui/2024.3.10", "files": [".nupkg.metadata", ".signature.p7s", "License.md", "Readme.md", "images/PackageIcon.png", "nice3point.revit.api.revitapiui.2024.3.10.nupkg.sha512", "nice3point.revit.api.revitapiui.nuspec", "ref/net48/RevitAPIUI.dll", "ref/net48/RevitAPIUI.xml"]}, "Nice3point.Revit.Build.Tasks/2.0.2": {"sha512": "ordUQs9JHk/oZ+HUhBP6GnD6WRVUH064EEI9kpPJnT7EoqYSXYnS/yCBdDb37mooRsJMmxDxP0M/B69hgJf2sA==", "type": "package", "path": "nice3point.revit.build.tasks/2.0.2", "files": [".nupkg.metadata", ".signature.p7s", "License.md", "Readme.md", "build/Nice3point.Revit.Build.Tasks.props", "build/Nice3point.Revit.Build.Tasks.targets", "build/Nice3point.Revit.Common.props", "build/Nice3point.Revit.GenerateCompatibleDefineConstants.targets", "build/Nice3point.Revit.ImplicitUsings.targets", "build/Nice3point.Revit.Publish.targets", "images/PackageIcon.png", "nice3point.revit.build.tasks.2.0.2.nupkg.sha512", "nice3point.revit.build.tasks.nuspec", "tasks/net48/Nice3point.Revit.Build.Tasks.dll", "tasks/net8.0/Nice3point.Revit.Build.Tasks.dll"]}, "Nice3point.Revit.Extensions/2024.2.1": {"sha512": "frDPEGq2vikIsHxp42OiKLJOm/YmYWd/AtHP6xRmAZCQilfGc5bbnKBokxNXvvr1a0yKCV2/4VoVomkROu/NtA==", "type": "package", "path": "nice3point.revit.extensions/2024.2.1", "files": [".nupkg.metadata", ".signature.p7s", "License.md", "Readme.md", "images/PackageIcon.png", "lib/net48/Nice3point.Revit.Extensions.dll", "lib/net48/Nice3point.Revit.Extensions.xml", "nice3point.revit.extensions.2024.2.1.nupkg.sha512", "nice3point.revit.extensions.nuspec"]}, "Nice3point.Revit.Toolkit/2024.2.0": {"sha512": "A54JYZSNLwaAl5OEOLGBwczNHSJbp0o4n9SnOH3XTYYPdwnZWu0HvGNZg4LWi78QofYGGUW6WnQagPU4Atq//w==", "type": "package", "path": "nice3point.revit.toolkit/2024.2.0", "files": [".nupkg.metadata", ".signature.p7s", "License.md", "Readme.md", "images/PackageIcon.png", "lib/net48/Nice3point.Revit.Toolkit.dll", "lib/net48/Nice3point.Revit.Toolkit.xml", "nice3point.revit.toolkit.2024.2.0.nupkg.sha512", "nice3point.revit.toolkit.nuspec"]}, "System.Buffers/4.6.0": {"sha512": "lN6tZi7Q46zFzAbRYXTIvfXcyvQQgxnY7Xm6C6xQ9784dEL1amjM6S6Iw4ZpsvesAKnRVsM4scrDQaDqSClkjA==", "type": "package", "path": "system.buffers/4.6.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "buildTransitive/net461/System.Buffers.targets", "buildTransitive/net462/_._", "lib/net462/System.Buffers.dll", "lib/net462/System.Buffers.xml", "lib/netcoreapp2.1/_._", "lib/netstandard2.0/System.Buffers.dll", "lib/netstandard2.0/System.Buffers.xml", "system.buffers.4.6.0.nupkg.sha512", "system.buffers.nuspec"]}, "System.ComponentModel.Annotations/5.0.0": {"sha512": "dMkqfy2el8A8/I76n2Hi1oBFEbG1SfxD2l5nhwXV3XjlnOmwxJlQbYpJH4W51odnU9sARCSAgv7S3CyAFMkpYg==", "type": "package", "path": "system.componentmodel.annotations/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net461/System.ComponentModel.Annotations.dll", "lib/netcore50/System.ComponentModel.Annotations.dll", "lib/netstandard1.4/System.ComponentModel.Annotations.dll", "lib/netstandard2.0/System.ComponentModel.Annotations.dll", "lib/netstandard2.1/System.ComponentModel.Annotations.dll", "lib/netstandard2.1/System.ComponentModel.Annotations.xml", "lib/portable-net45+win8/_._", "lib/win8/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net461/System.ComponentModel.Annotations.dll", "ref/net461/System.ComponentModel.Annotations.xml", "ref/netcore50/System.ComponentModel.Annotations.dll", "ref/netcore50/System.ComponentModel.Annotations.xml", "ref/netcore50/de/System.ComponentModel.Annotations.xml", "ref/netcore50/es/System.ComponentModel.Annotations.xml", "ref/netcore50/fr/System.ComponentModel.Annotations.xml", "ref/netcore50/it/System.ComponentModel.Annotations.xml", "ref/netcore50/ja/System.ComponentModel.Annotations.xml", "ref/netcore50/ko/System.ComponentModel.Annotations.xml", "ref/netcore50/ru/System.ComponentModel.Annotations.xml", "ref/netcore50/zh-hans/System.ComponentModel.Annotations.xml", "ref/netcore50/zh-hant/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/System.ComponentModel.Annotations.dll", "ref/netstandard1.1/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/de/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/es/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/fr/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/it/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/ja/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/ko/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/ru/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/zh-hans/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/zh-hant/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/System.ComponentModel.Annotations.dll", "ref/netstandard1.3/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/de/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/es/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/fr/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/it/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/ja/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/ko/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/ru/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/zh-hans/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/zh-hant/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/System.ComponentModel.Annotations.dll", "ref/netstandard1.4/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/de/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/es/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/fr/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/it/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/ja/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/ko/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/ru/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/zh-hans/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/zh-hant/System.ComponentModel.Annotations.xml", "ref/netstandard2.0/System.ComponentModel.Annotations.dll", "ref/netstandard2.0/System.ComponentModel.Annotations.xml", "ref/netstandard2.1/System.ComponentModel.Annotations.dll", "ref/netstandard2.1/System.ComponentModel.Annotations.xml", "ref/portable-net45+win8/_._", "ref/win8/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.componentmodel.annotations.5.0.0.nupkg.sha512", "system.componentmodel.annotations.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Memory/4.6.0": {"sha512": "OEkbBQoklHngJ8UD8ez2AERSk2g+/qpAaSWWCBFbpH727HxDq5ydVkuncBaKcKfwRqXGWx64dS6G1SUScMsitg==", "type": "package", "path": "system.memory/4.6.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "buildTransitive/net461/System.Memory.targets", "buildTransitive/net462/_._", "lib/net462/System.Memory.dll", "lib/net462/System.Memory.xml", "lib/netcoreapp2.1/_._", "lib/netstandard2.0/System.Memory.dll", "lib/netstandard2.0/System.Memory.xml", "system.memory.4.6.0.nupkg.sha512", "system.memory.nuspec"]}, "System.Numerics.Vectors/4.6.0": {"sha512": "t+SoieZsRuEyiw/J+qXUbolyO219tKQQI0+2/YI+Qv7YdGValA6WiuokrNKqjrTNsy5ABWU11bdKOzUdheteXg==", "type": "package", "path": "system.numerics.vectors/4.6.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "buildTransitive/net461/System.Numerics.Vectors.targets", "buildTransitive/net462/_._", "lib/net462/System.Numerics.Vectors.dll", "lib/net462/System.Numerics.Vectors.xml", "lib/netcoreapp2.0/_._", "lib/netstandard2.0/System.Numerics.Vectors.dll", "lib/netstandard2.0/System.Numerics.Vectors.xml", "system.numerics.vectors.4.6.0.nupkg.sha512", "system.numerics.vectors.nuspec"]}, "System.Resources.Extensions/8.0.0": {"sha512": "psnQ6GRQOvt+evda5C4nD5EuV49mz2Tv0DD2JDVDEbE/TKoMukxSkGJcsBJ0pajpPuFRr67syFYlkJ4Wj6A5Zw==", "type": "package", "path": "system.resources.extensions/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Resources.Extensions.targets", "buildTransitive/net462/System.Resources.Extensions.targets", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Resources.Extensions.targets", "lib/net462/System.Resources.Extensions.dll", "lib/net462/System.Resources.Extensions.xml", "lib/net6.0/System.Resources.Extensions.dll", "lib/net6.0/System.Resources.Extensions.xml", "lib/net7.0/System.Resources.Extensions.dll", "lib/net7.0/System.Resources.Extensions.xml", "lib/net8.0/System.Resources.Extensions.dll", "lib/net8.0/System.Resources.Extensions.xml", "lib/netstandard2.0/System.Resources.Extensions.dll", "lib/netstandard2.0/System.Resources.Extensions.xml", "system.resources.extensions.8.0.0.nupkg.sha512", "system.resources.extensions.nuspec", "useSharedDesignerContext.txt"]}, "System.Runtime.CompilerServices.Unsafe/6.1.0": {"sha512": "5o/HZxx6RVqYlhKSq8/zronDkALJZUT2Vz0hx43f0gwe8mwlM0y2nYlqdBwLMzr262Bwvpikeb/yEwkAa5PADg==", "type": "package", "path": "system.runtime.compilerservices.unsafe/6.1.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "buildTransitive/net461/System.Runtime.CompilerServices.Unsafe.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Runtime.CompilerServices.Unsafe.targets", "lib/net462/System.Runtime.CompilerServices.Unsafe.dll", "lib/net462/System.Runtime.CompilerServices.Unsafe.xml", "lib/net7.0/_._", "lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll", "lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.xml", "system.runtime.compilerservices.unsafe.6.1.0.nupkg.sha512", "system.runtime.compilerservices.unsafe.nuspec"]}, "System.Threading.Tasks.Extensions/4.5.4": {"sha512": "zteT+G8xuGu6mS+mzDzYXbzS7rd3K6Fjb9RiZlYlJPam2/hU7JCBZBVEcywNuR+oZ1ncTvc/cq0faRr3P01OVg==", "type": "package", "path": "system.threading.tasks.extensions/4.5.4", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net461/System.Threading.Tasks.Extensions.dll", "lib/net461/System.Threading.Tasks.Extensions.xml", "lib/netcoreapp2.1/_._", "lib/netstandard1.0/System.Threading.Tasks.Extensions.dll", "lib/netstandard1.0/System.Threading.Tasks.Extensions.xml", "lib/netstandard2.0/System.Threading.Tasks.Extensions.dll", "lib/netstandard2.0/System.Threading.Tasks.Extensions.xml", "lib/portable-net45+win8+wp8+wpa81/System.Threading.Tasks.Extensions.dll", "lib/portable-net45+win8+wp8+wpa81/System.Threading.Tasks.Extensions.xml", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/netcoreapp2.1/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.threading.tasks.extensions.4.5.4.nupkg.sha512", "system.threading.tasks.extensions.nuspec", "useSharedDesignerContext.txt", "version.txt"]}}, "projectFileDependencyGroups": {".NETFramework,Version=v4.8": ["CommunityToolkit.Mvvm >= 8.4.0", "DocumentFormat.OpenXml >= 3.0.1", "MaterialDesignThemes >= 5.2.1", "Microsoft.Office.Interop.Excel >= 15.0.4795.1001", "Nice3point.Revit.Api.RevitAPI >= 2024.*", "Nice3point.Revit.Api.RevitAPIUI >= 2024.*", "Nice3point.Revit.Build.Tasks >= 2.0.2", "Nice3point.Revit.Extensions >= 2024.*", "Nice3point.Revit.Toolkit >= 2024.*", "System.ComponentModel.Annotations >= 5.0.0", "System.Resources.Extensions >= 8.0.0"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\PB6_WPF\\MEP.PowerBIM_6\\MEP.PowerBIM_6.csproj", "projectName": "MEP.PowerBIM_6", "projectPath": "C:\\Users\\<USER>\\Desktop\\PB6_WPF\\MEP.PowerBIM_6\\MEP.PowerBIM_6.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\PB6_WPF\\MEP.PowerBIM_6\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net48"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net48": {"targetAlias": "net48", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net48": {"targetAlias": "net48", "dependencies": {"CommunityToolkit.Mvvm": {"target": "Package", "version": "[8.4.0, )"}, "DocumentFormat.OpenXml": {"target": "Package", "version": "[3.0.1, )"}, "MaterialDesignThemes": {"target": "Package", "version": "[5.2.1, )"}, "Microsoft.Office.Interop.Excel": {"target": "Package", "version": "[15.0.4795.1001, )"}, "Nice3point.Revit.Api.RevitAPI": {"target": "Package", "version": "[2024.*, )"}, "Nice3point.Revit.Api.RevitAPIUI": {"target": "Package", "version": "[2024.*, )"}, "Nice3point.Revit.Build.Tasks": {"target": "Package", "version": "[2.0.2, )"}, "Nice3point.Revit.Extensions": {"target": "Package", "version": "[2024.*, )"}, "Nice3point.Revit.Toolkit": {"target": "Package", "version": "[2024.*, )"}, "System.ComponentModel.Annotations": {"target": "Package", "version": "[5.0.0, )"}, "System.Resources.Extensions": {"target": "Package", "version": "[8.0.0, )"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.204\\RuntimeIdentifierGraph.json"}}}}