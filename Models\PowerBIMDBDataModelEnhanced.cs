﻿using Autodesk.Revit.DB.Electrical;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MEP.PowerBIM_6.Models
{
    /// <summary>
    /// Enhanced model representing a Distribution Board for WPF data binding
    /// </summary>
    public class PowerBIMDBDataModelEnhanced : ObservableObject
    {
        #region Private Fields

        private bool _isSelected;
        private string _schedule_DB_Name = string.Empty;
        private int _result_PassCount;
        private int _result_WarningCount;
        private int _result_FailCount;
        private string _gui_Notes = string.Empty;
        private bool _update_Required;
        private string _user_Notes = string.Empty;
        private string _gui_Notes_Message = string.Empty;
        private bool _data_Good = true;
        private bool _parameters_Missing;
        private bool _db_All_Circuits_Pass;
        private string _db_Check_Result = string.Empty;
        private string _db_Check_Warnings = string.Empty;
        private string _db_Result_Summary = string.Empty;
        private bool _isLocked;
        private bool _isManuallyLocked;
        private string _circuitNaming = string.Empty;
        private double _upstreamDeviceRating;
        private double _deviceKARating;
        private double _efliR;
        private double _efliX;
        private double _dbVD;
        private double _pscc;
        private int _numberOfWays;

        private ObservableCollection<PowerBIMCircuitDataModel> _circuits;

        #endregion

        #region Constructor

        public PowerBIMDBDataModelEnhanced()
        {
            _circuits = new ObservableCollection<PowerBIMCircuitDataModel>();
        }

        #endregion

        #region UI Properties

        /// <summary>
        /// Gets or sets whether this DB is selected
        /// </summary>
        public bool IsSelected
        {
            get => _isSelected;
            set => SetProperty(ref _isSelected, value);
        }

        /// <summary>
        /// Gets or sets the DB schedule name
        /// </summary>
        [Required]
        public string Schedule_DB_Name
        {
            get => _schedule_DB_Name;
            set => SetProperty(ref _schedule_DB_Name, value);
        }

        /// <summary>
        /// Gets or sets the number of passed circuits
        /// </summary>
        public int Result_PassCount
        {
            get => _result_PassCount;
            set
            {
                if (SetProperty(ref _result_PassCount, value))
                {
                    OnPropertyChanged(nameof(TotalCircuitCount));
                    OnPropertyChanged(nameof(AllCircuitsPass));
                    OnPropertyChanged(nameof(StatusColor));
                }
            }
        }

        /// <summary>
        /// Gets or sets the number of warning circuits
        /// </summary>
        public int Result_WarningCount
        {
            get => _result_WarningCount;
            set
            {
                if (SetProperty(ref _result_WarningCount, value))
                {
                    OnPropertyChanged(nameof(TotalCircuitCount));
                    OnPropertyChanged(nameof(AllCircuitsPass));
                    OnPropertyChanged(nameof(StatusColor));
                }
            }
        }

        /// <summary>
        /// Gets or sets the number of failed circuits
        /// </summary>
        public int Result_FailCount
        {
            get => _result_FailCount;
            set
            {
                if (SetProperty(ref _result_FailCount, value))
                {
                    OnPropertyChanged(nameof(TotalCircuitCount));
                    OnPropertyChanged(nameof(AllCircuitsPass));
                    OnPropertyChanged(nameof(StatusColor));
                }
            }
        }

        /// <summary>
        /// Gets or sets the GUI notes
        /// </summary>
        public string GUI_Notes
        {
            get => _gui_Notes;
            set => SetProperty(ref _gui_Notes, value);
        }

        /// <summary>
        /// Gets or sets the GUI notes message
        /// </summary>
        public string GUI_Notes_Message
        {
            get => _gui_Notes_Message;
            set => SetProperty(ref _gui_Notes_Message, value);
        }

        /// <summary>
        /// Gets or sets whether an update is required
        /// </summary>
        public bool Update_Required
        {
            get => _update_Required;
            set => SetProperty(ref _update_Required, value);
        }

        /// <summary>
        /// Gets or sets user notes
        /// </summary>
        public string User_Notes
        {
            get => _user_Notes;
            set => SetProperty(ref _user_Notes, value);
        }

        #endregion

        #region Technical Properties

        /// <summary>
        /// Gets or sets whether the data is good
        /// </summary>
        public bool Data_Good
        {
            get => _data_Good;
            set => SetProperty(ref _data_Good, value);
        }

        /// <summary>
        /// Gets or sets whether parameters are missing
        /// </summary>
        public bool Parameters_Missing
        {
            get => _parameters_Missing;
            set => SetProperty(ref _parameters_Missing, value);
        }

        /// <summary>
        /// Gets or sets whether all circuits pass
        /// </summary>
        public bool DB_All_Circuits_Pass
        {
            get => _db_All_Circuits_Pass;
            set => SetProperty(ref _db_All_Circuits_Pass, value);
        }

        /// <summary>
        /// Gets or sets the DB check result
        /// </summary>
        public string DB_Check_Result
        {
            get => _db_Check_Result;
            set => SetProperty(ref _db_Check_Result, value);
        }

        /// <summary>
        /// Gets or sets the DB check warnings
        /// </summary>
        public string DB_Check_Warnings
        {
            get => _db_Check_Warnings;
            set => SetProperty(ref _db_Check_Warnings, value);
        }

        /// <summary>
        /// Gets or sets the DB result summary
        /// </summary>
        public string DB_Result_Summary
        {
            get => _db_Result_Summary;
            set => SetProperty(ref _db_Result_Summary, value);
        }

        /// <summary>
        /// Gets or sets whether the DB is locked
        /// </summary>
        public bool IsLocked
        {
            get => _isLocked;
            set => SetProperty(ref _isLocked, value);
        }

        /// <summary>
        /// Gets or sets whether the DB is manually locked
        /// </summary>
        public bool IsManuallyLocked
        {
            get => _isManuallyLocked;
            set => SetProperty(ref _isManuallyLocked, value);
        }

        /// <summary>
        /// Gets or sets the circuit naming convention
        /// </summary>
        public string CircuitNaming
        {
            get => _circuitNaming;
            set => SetProperty(ref _circuitNaming, value);
        }

        /// <summary>
        /// Gets or sets the upstream device rating
        /// </summary>
        [Range(0, double.MaxValue)]
        public double UpstreamDeviceRating
        {
            get => _upstreamDeviceRating;
            set => SetProperty(ref _upstreamDeviceRating, value);
        }

        /// <summary>
        /// Gets or sets the device kA rating
        /// </summary>
        [Range(0, double.MaxValue)]
        public double DeviceKARating
        {
            get => _deviceKARating;
            set => SetProperty(ref _deviceKARating, value);
        }

        /// <summary>
        /// Gets or sets the EFLI R value
        /// </summary>
        public double EFLI_R
        {
            get => _efliR;
            set => SetProperty(ref _efliR, value);
        }

        /// <summary>
        /// Gets or sets the EFLI X value
        /// </summary>
        public double EFLI_X
        {
            get => _efliX;
            set => SetProperty(ref _efliX, value);
        }

        /// <summary>
        /// Gets or sets the DB voltage drop
        /// </summary>
        public double DBVD
        {
            get => _dbVD;
            set => SetProperty(ref _dbVD, value);
        }

        /// <summary>
        /// Gets or sets the PSCC value
        /// </summary>
        public double PSCC
        {
            get => _pscc;
            set => SetProperty(ref _pscc, value);
        }

        /// <summary>
        /// Gets or sets the number of ways
        /// </summary>
        public int NumberOfWays
        {
            get => _numberOfWays;
            set => SetProperty(ref _numberOfWays, value);
        }

        #endregion

        #region Circuit Collection

        /// <summary>
        /// Gets the collection of circuits in this distribution board
        /// </summary>
        public ObservableCollection<PowerBIMCircuitDataModel> Circuits
        {
            get => _circuits;
            set => SetProperty(ref _circuits, value);
        }

        #endregion

        #region Revit Integration Properties

        /// <summary>
        /// Gets or sets the Revit DB element
        /// </summary>
        public Element DB_Element { get; set; }

        /// <summary>
        /// Gets or sets the electrical system
        /// </summary>
        public ElectricalSystem DB_ElectricalSystem { get; set; }

        /// <summary>
        /// Gets or sets the panel schedule view
        /// </summary>
        public PanelScheduleView PanelScheduleView { get; set; }

        /// <summary>
        /// Gets or sets the DB location
        /// </summary>
        public XYZ DB_Location { get; set; }

        /// <summary>
        /// Gets or sets the project info reference
        /// </summary>
        public PowerBIMProjectInfoModel ProjectInfo { get; set; }

        #endregion

        #region Computed Properties

        /// <summary>
        /// Gets the total circuit count
        /// </summary>
        public int TotalCircuitCount => Result_PassCount + Result_WarningCount + Result_FailCount;

        /// <summary>
        /// Gets whether all circuits pass
        /// </summary>
        public bool AllCircuitsPass => Result_FailCount == 0 && Result_WarningCount == 0 && TotalCircuitCount > 0;

        /// <summary>
        /// Gets the status color based on circuit results
        /// </summary>
        public string StatusColor
        {
            get
            {
                if (Result_FailCount > 0) return "Red";
                if (Result_WarningCount > 0) return "Orange";
                if (Result_PassCount > 0) return "Green";
                return "Gray";
            }
        }

        /// <summary>
        /// Gets the display name with lock indicator
        /// </summary>
        public string DisplayName
        {
            get
            {
                var name = Schedule_DB_Name;
                if (IsLocked || IsManuallyLocked)
                    name = "(in use) " + name;
                return name;
            }
        }

        /// <summary>
        /// Gets whether the DB has custom circuit path
        /// </summary>
        public bool HasCustomCircuitPath => DB_ElectricalSystem?.HasCustomCircuitPath ?? false;

        #endregion

        #region Public Methods

        /// <summary>
        /// Updates the circuit counts based on the current circuits collection
        /// </summary>
        public void UpdateCircuitCounts()
        {
            if (Circuits == null || !Circuits.Any())
            {
                Result_PassCount = 0;
                Result_WarningCount = 0;
                Result_FailCount = 0;
                return;
            }

            Result_PassCount = Circuits.Count(c => c.CheckResult == "Pass");
            Result_WarningCount = Circuits.Count(c => c.CheckResult == "Warning");
            Result_FailCount = Circuits.Count(c => c.CheckResult == "Fail");
        }

        /// <summary>
        /// Refreshes all computed properties
        /// </summary>
        public void RefreshProperties()
        {
            OnPropertyChanged(nameof(TotalCircuitCount));
            OnPropertyChanged(nameof(AllCircuitsPass));
            OnPropertyChanged(nameof(StatusColor));
            OnPropertyChanged(nameof(DisplayName));
        }

        #endregion
    }
}
