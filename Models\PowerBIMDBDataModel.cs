﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using CommunityToolkit.Mvvm.ComponentModel;

namespace MEP.PowerBIM_6.Models
{
    /// <summary>
    /// Model representing a Distribution Board for WPF data binding
    /// </summary>
    public class PowerBIMDBDataModel : ObservableObject
    {
        private bool _isSelected;
        private string _schedule_DB_Name = string.Empty;
        private int _result_PassCount;
        private int _result_WarningCount;
        private int _result_FailCount;
        private string _gui_Notes = string.Empty;
        private bool _update_Required;
        private string _user_Notes = string.Empty;

        /// <summary>
        /// Gets or sets whether this DB is selected
        /// </summary>
        public bool IsSelected
        {
            get => _isSelected;
            set => SetProperty(ref _isSelected, value);
        }

        /// <summary>
        /// Gets or sets the DB schedule name
        /// </summary>
        [Required]
        public string Schedule_DB_Name
        {
            get => _schedule_DB_Name;
            set => SetProperty(ref _schedule_DB_Name, value);
        }

        /// <summary>
        /// Gets or sets the number of passed circuits
        /// </summary>
        public int Result_PassCount
        {
            get => _result_PassCount;
            set => SetProperty(ref _result_PassCount, value);
        }

        /// <summary>
        /// Gets or sets the number of warning circuits
        /// </summary>
        public int Result_WarningCount
        {
            get => _result_WarningCount;
            set => SetProperty(ref _result_WarningCount, value);
        }

        /// <summary>
        /// Gets or sets the number of failed circuits
        /// </summary>
        public int Result_FailCount
        {
            get => _result_FailCount;
            set => SetProperty(ref _result_FailCount, value);
        }

        /// <summary>
        /// Gets or sets the GUI notes
        /// </summary>
        public string GUI_Notes
        {
            get => _gui_Notes;
            set => SetProperty(ref _gui_Notes, value);
        }

        /// <summary>
        /// Gets or sets whether an update is required
        /// </summary>
        public bool Update_Required
        {
            get => _update_Required;
            set => SetProperty(ref _update_Required, value);
        }

        /// <summary>
        /// Gets or sets user notes
        /// </summary>
        public string User_Notes
        {
            get => _user_Notes;
            set => SetProperty(ref _user_Notes, value);
        }

        /// <summary>
        /// Gets the total circuit count
        /// </summary>
        public int TotalCircuitCount => Result_PassCount + Result_WarningCount + Result_FailCount;

        /// <summary>
        /// Gets whether all circuits pass
        /// </summary>
        public bool AllCircuitsPass => Result_FailCount == 0 && Result_WarningCount == 0 && TotalCircuitCount > 0;

        /// <summary>
        /// Gets the status color based on circuit results
        /// </summary>
        public string StatusColor
        {
            get
            {
                if (Result_FailCount > 0) return "Red";
                if (Result_WarningCount > 0) return "Orange";
                if (Result_PassCount > 0) return "Green";
                return "Gray";
            }
        }
    }
}
