﻿<Window
    x:Class="MEP.PowerBIM_6.Views.PB6_MainWindow"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="clr-namespace:MEP.PowerBIM_6.Views"
    xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    Title="Beca Tools | Building Services | Electrical"
    Width="1000"
    Height="600" 
    WindowStartupLocation="CenterScreen"
    mc:Ignorable="d">

    <Window.Resources>
        <ResourceDictionary Source="pack://application:,,,/Common.UI.WPF;component/UI/Dictionaries/BecaMainDictionary.xaml" />
    </Window.Resources>

    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="139*"/>
            <ColumnDefinition Width="861*"/>
        </Grid.ColumnDefinitions>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>

        <!--  Header  -->
        <Grid Grid.ColumnSpan="2">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="*" />
            </Grid.ColumnDefinitions>

            <TextBlock
                Grid.ColumnSpan="2"
                Margin="10,0,0,0"
                Style="{StaticResource MaterialDesignHeadline4TextBlock}"
                Text="POWER BIM" />
            
            <Button 
                Grid.ColumnSpan="2"
                Height="45"
                HorizontalAlignment="Right"
                VerticalAlignment="Top"
                Background="Transparent"
                BorderBrush="Transparent"
                Click="OpenUrlButton_Click"
                Content="{materialDesign:PackIcon Kind=HelpCircleOutline,
                                                  Size=38}"
                Foreground="#12A8B2" />

            <Separator
                Grid.ColumnSpan="2"
                Margin="10,45,10,0"
                Background="#FFCE00">
                <Separator.LayoutTransform>
                    <ScaleTransform ScaleY="3.5" />
                </Separator.LayoutTransform>
            </Separator>
        </Grid>

        <!-- Frame -->
        <Grid Grid.Row="1">
            <Frame x:Name="MainFrame" NavigationUIVisibility="Hidden" />
        </Grid>

        <!--  Footer  -->
        <Grid Grid.Row="2" Grid.ColumnSpan="2" Margin="0,484,0,0" Grid.RowSpan="2">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="*" />
            </Grid.ColumnDefinitions>

            <TextBlock
                Grid.Column="1"
                Margin="0,15,15,10"
                HorizontalAlignment="Right"
                FontSize="20"
                FontWeight="DemiBold"
                Text="Make Everyday Better" />

            <Image
                Width="116"
                Height="25"
                Margin="15,15,0,10"
                HorizontalAlignment="Left"
                RenderOptions.BitmapScalingMode="HighQuality"
                SnapsToDevicePixels="True"
                Source="/MEP.PowerBIM_6;component/Views/BecaLogoBlack.png" />
        </Grid>
    </Grid>
</Window>
