﻿using MEP.PowerBIM_6.Models;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MEP.PowerBIM_6.Services
{
    /// <summary>
    /// Service for validating PowerBIM data integrity and consistency
    /// </summary>
    public class PowerBIMValidationService
    {
        #region Validation Results

        public class ValidationResult
        {
            public bool IsValid { get; set; }
            public List<string> Errors { get; set; } = new List<string>();
            public List<string> Warnings { get; set; } = new List<string>();
            public List<string> Information { get; set; } = new List<string>();

            public bool HasErrors => Errors.Any();
            public bool HasWarnings => Warnings.Any();
            public int TotalIssues => Errors.Count + Warnings.Count;
        }

        #endregion

        #region Project Info Validation

        /// <summary>
        /// Validates project information
        /// </summary>
        public ValidationResult ValidateProjectInfo(PowerBIMProjectInfoModel projectInfo)
        {
            var result = new ValidationResult { IsValid = true };

            if (projectInfo == null)
            {
                result.Errors.Add("Project information is null");
                result.IsValid = false;
                return result;
            }

            // Validate required fields
            if (string.IsNullOrWhiteSpace(projectInfo.JobName))
            {
                result.Warnings.Add("Job Name is not specified");
            }

            if (string.IsNullOrWhiteSpace(projectInfo.JobNumber))
            {
                result.Warnings.Add("Job Number is not specified");
            }

            if (string.IsNullOrWhiteSpace(projectInfo.Engineer))
            {
                result.Warnings.Add("Engineer is not specified");
            }

            // Validate numerical parameters
            if (projectInfo.DiscriminationTestMultiplier <= 0 || projectInfo.DiscriminationTestMultiplier > 5)
            {
                result.Errors.Add("Discrimination Test Multiplier must be between 0 and 5");
                result.IsValid = false;
            }

            if (projectInfo.AmbientTemp < -50 || projectInfo.AmbientTemp > 100)
            {
                result.Errors.Add("Ambient Temperature must be between -50°C and 100°C");
                result.IsValid = false;
            }

            // Validate database path
            if (string.IsNullOrWhiteSpace(projectInfo.DatabasePath))
            {
                result.Warnings.Add("Database path is not specified");
            }
            else if (!System.IO.File.Exists(projectInfo.DatabasePath))
            {
                result.Errors.Add($"Database file does not exist: {projectInfo.DatabasePath}");
                result.IsValid = false;
            }

            return result;
        }

        #endregion

        #region Distribution Board Validation

        /// <summary>
        /// Validates a single distribution board
        /// </summary>
        public ValidationResult ValidateDistributionBoard(PowerBIMDBDataModelEnhanced db)
        {
            var result = new ValidationResult { IsValid = true };

            if (db == null)
            {
                result.Errors.Add("Distribution board is null");
                result.IsValid = false;
                return result;
            }

            // Validate basic properties
            if (string.IsNullOrWhiteSpace(db.Schedule_DB_Name))
            {
                result.Errors.Add("Distribution board name is required");
                result.IsValid = false;
            }

            // Validate electrical parameters
            if (db.UpstreamDeviceRating <= 0)
            {
                result.Errors.Add("Upstream Device Rating must be greater than 0");
                result.IsValid = false;
            }

            if (db.DeviceKARating <= 0)
            {
                result.Errors.Add("Device kA Rating must be greater than 0");
                result.IsValid = false;
            }

            if (db.EFLI_R < 0)
            {
                result.Errors.Add("EFLI R value cannot be negative");
                result.IsValid = false;
            }

            if (db.EFLI_X < 0)
            {
                result.Errors.Add("EFLI X value cannot be negative");
                result.IsValid = false;
            }

            if (db.DBVD < 0 || db.DBVD > 100)
            {
                result.Errors.Add("DB Voltage Drop must be between 0% and 100%");
                result.IsValid = false;
            }

            if (db.PSCC <= 0)
            {
                result.Errors.Add("PSCC must be greater than 0");
                result.IsValid = false;
            }

            // Validate realistic ranges
            if (db.UpstreamDeviceRating > 1000)
            {
                result.Warnings.Add("Upstream Device Rating is unusually high (>1000A)");
            }

            if (db.EFLI_R > 10)
            {
                result.Warnings.Add("EFLI R value is unusually high (>10Ω)");
            }

            if (db.EFLI_X > 10)
            {
                result.Warnings.Add("EFLI X value is unusually high (>10Ω)");
            }

            if (db.DBVD > 10)
            {
                result.Warnings.Add("DB Voltage Drop is unusually high (>10%)");
            }

            // Validate circuits
            if (db.Circuits != null && db.Circuits.Any())
            {
                var circuitValidation = ValidateCircuits(db.Circuits);
                result.Errors.AddRange(circuitValidation.Errors.Select(e => $"Circuit validation: {e}"));
                result.Warnings.AddRange(circuitValidation.Warnings.Select(w => $"Circuit validation: {w}"));

                if (!circuitValidation.IsValid)
                    result.IsValid = false;
            }

            return result;
        }

        /// <summary>
        /// Validates all distribution boards
        /// </summary>
        public ValidationResult ValidateDistributionBoards(ObservableCollection<PowerBIMDBDataModelEnhanced> distributionBoards)
        {
            var result = new ValidationResult { IsValid = true };

            if (distributionBoards == null || !distributionBoards.Any())
            {
                result.Warnings.Add("No distribution boards found");
                return result;
            }

            // Check for duplicate names
            var duplicateNames = distributionBoards
                .GroupBy(db => db.Schedule_DB_Name)
                .Where(g => g.Count() > 1)
                .Select(g => g.Key)
                .ToList();

            foreach (var duplicateName in duplicateNames)
            {
                result.Errors.Add($"Duplicate distribution board name: {duplicateName}");
                result.IsValid = false;
            }

            // Validate each distribution board
            foreach (var db in distributionBoards)
            {
                var dbValidation = ValidateDistributionBoard(db);
                result.Errors.AddRange(dbValidation.Errors.Select(e => $"{db.Schedule_DB_Name}: {e}"));
                result.Warnings.AddRange(dbValidation.Warnings.Select(w => $"{db.Schedule_DB_Name}: {w}"));

                if (!dbValidation.IsValid)
                    result.IsValid = false;
            }

            // Summary information
            result.Information.Add($"Total distribution boards: {distributionBoards.Count}");
            result.Information.Add($"Selected distribution boards: {distributionBoards.Count(db => db.IsSelected)}");
            result.Information.Add($"Locked distribution boards: {distributionBoards.Count(db => db.IsManuallyLocked)}");

            return result;
        }

        #endregion

        #region Circuit Validation

        /// <summary>
        /// Validates a single circuit
        /// </summary>
        public ValidationResult ValidateCircuit(PowerBIMCircuitDataModel circuit)
        {
            var result = new ValidationResult { IsValid = true };

            if (circuit == null)
            {
                result.Errors.Add("Circuit is null");
                result.IsValid = false;
                return result;
            }

            // Validate basic properties
            if (string.IsNullOrWhiteSpace(circuit.CCT_Number))
            {
                result.Errors.Add("Circuit number is required");
                result.IsValid = false;
            }

            if (circuit.Number_Of_Poles <= 0 || circuit.Number_Of_Poles > 3)
            {
                result.Errors.Add("Number of poles must be 1, 2, or 3");
                result.IsValid = false;
            }

            if (circuit.Number_Of_Elements <= 0)
            {
                result.Errors.Add("Number of elements must be greater than 0");
                result.IsValid = false;
            }

            // Validate current values
            if (circuit.ManualCurrent && circuit.Manual_PowerBim_User_Current <= 0)
            {
                result.Errors.Add("Manual current must be greater than 0 when manual current is enabled");
                result.IsValid = false;
            }

            if (circuit.EffectiveCurrent <= 0)
            {
                result.Errors.Add("Effective current must be greater than 0");
                result.IsValid = false;
            }

            // Validate realistic ranges
            if (circuit.EffectiveCurrent > 1000)
            {
                result.Warnings.Add("Circuit current is unusually high (>1000A)");
            }

            if (circuit.Number_Of_Elements > 50)
            {
                result.Warnings.Add("Number of elements is unusually high (>50)");
            }

            // Validate trip rating
            if (circuit.Schedule_Trip_Rating <= 0)
            {
                result.Warnings.Add("Trip rating should be specified");
            }
            else if (circuit.Schedule_Trip_Rating < circuit.EffectiveCurrent * 1.25)
            {
                result.Warnings.Add("Trip rating may be too low for circuit current");
            }

            return result;
        }

        /// <summary>
        /// Validates all circuits in a collection
        /// </summary>
        public ValidationResult ValidateCircuits(ObservableCollection<PowerBIMCircuitDataModel> circuits)
        {
            var result = new ValidationResult { IsValid = true };

            if (circuits == null || !circuits.Any())
            {
                result.Information.Add("No circuits found");
                return result;
            }

            // Check for duplicate circuit numbers
            var duplicateNumbers = circuits
                .Where(c => !string.IsNullOrWhiteSpace(c.CCT_Number))
                .GroupBy(c => c.CCT_Number)
                .Where(g => g.Count() > 1)
                .Select(g => g.Key)
                .ToList();

            foreach (var duplicateNumber in duplicateNumbers)
            {
                result.Errors.Add($"Duplicate circuit number: {duplicateNumber}");
                result.IsValid = false;
            }

            // Validate each circuit
            foreach (var circuit in circuits)
            {
                var circuitValidation = ValidateCircuit(circuit);
                result.Errors.AddRange(circuitValidation.Errors.Select(e => $"Circuit {circuit.CCT_Number}: {e}"));
                result.Warnings.AddRange(circuitValidation.Warnings.Select(w => $"Circuit {circuit.CCT_Number}: {w}"));

                if (!circuitValidation.IsValid)
                    result.IsValid = false;
            }

            // Summary information
            result.Information.Add($"Total circuits: {circuits.Count}");
            result.Information.Add($"Spare/Space circuits: {circuits.Count(c => c.CCT_Is_Spare_Or_Space)}");
            result.Information.Add($"Manual current circuits: {circuits.Count(c => c.ManualCurrent)}");

            return result;
        }

        #endregion

        #region Complete Validation

        /// <summary>
        /// Performs complete validation of all PowerBIM data
        /// </summary>
        public ValidationResult ValidateComplete(
            PowerBIMProjectInfoModel projectInfo,
            ObservableCollection<PowerBIMDBDataModelEnhanced> distributionBoards)
        {
            var result = new ValidationResult { IsValid = true };

            // Validate project info
            var projectValidation = ValidateProjectInfo(projectInfo);
            result.Errors.AddRange(projectValidation.Errors);
            result.Warnings.AddRange(projectValidation.Warnings);
            result.Information.AddRange(projectValidation.Information);

            if (!projectValidation.IsValid)
                result.IsValid = false;

            // Validate distribution boards
            var dbValidation = ValidateDistributionBoards(distributionBoards);
            result.Errors.AddRange(dbValidation.Errors);
            result.Warnings.AddRange(dbValidation.Warnings);
            result.Information.AddRange(dbValidation.Information);

            if (!dbValidation.IsValid)
                result.IsValid = false;

            return result;
        }

        #endregion
    }
}
