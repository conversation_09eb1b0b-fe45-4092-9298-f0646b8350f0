using Autodesk.Revit.UI;
using BecaActivityLogger.CoreLogic.Data;
using MEP.PowerBIM_5.CoreLogic;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using CommunityToolkit.Mvvm.ComponentModel;

namespace MEP.PowerBIM_6.Models
{
    /// <summary>
    /// Voltage drop calculation methods
    /// </summary>
    public enum VoltDropCalculation
    {
        LinearDeprecating = 1,
        LumpLoad = 2,
        Unknown = 0
    }

    /// <summary>
    /// MVVM-compatible model for PowerBIM project information
    /// </summary>
    public class PowerBIMProjectInfoModel : ObservableObject
    {
        #region Private Fields

        private string _jobName = string.Empty;
        private string _jobNumber = string.Empty;
        private string _engineer = string.Empty;
        private string _verifier = string.Empty;
        private string _revision = string.Empty;
        private DateTime _date = DateTime.Now;

        private double _ambientTemp = 30.0;
        private double _systemVDMaxPerc = 0.05;
        private double _discriminationTestMultiplier = 1.25;
        private int _guiGenCPDRangeSelectedIndex = 0;
        private string _databasePath = string.Empty;

        private VoltDropCalculation _powerVDCalculation = VoltDropCalculation.LinearDeprecating;
        private VoltDropCalculation _lightingVDCalculation = VoltDropCalculation.LinearDeprecating;
        private VoltDropCalculation _otherVDCalculation = VoltDropCalculation.LinearDeprecating;

        private bool _gpoCalc80Perc = false;
        private bool _clearingTimePower04 = true;
        private bool _clearingTimeLighting04 = true;

        private bool _parametersChanged = false;
        private bool _databaseArrayIsPopulated = false;
        private string _criticalErrorMessage = string.Empty;

        #endregion

        #region Public Properties

        /// <summary>
        /// Gets or sets the job name
        /// </summary>
        [Required]
        public string JobName
        {
            get => _jobName;
            set => SetProperty(ref _jobName, value);
        }

        /// <summary>
        /// Gets or sets the job number
        /// </summary>
        [Required]
        public string JobNumber
        {
            get => _jobNumber;
            set => SetProperty(ref _jobNumber, value);
        }

        /// <summary>
        /// Gets or sets the engineer name
        /// </summary>
        public string Engineer
        {
            get => _engineer;
            set => SetProperty(ref _engineer, value);
        }

        /// <summary>
        /// Gets or sets the verifier name
        /// </summary>
        public string Verifier
        {
            get => _verifier;
            set => SetProperty(ref _verifier, value);
        }

        /// <summary>
        /// Gets or sets the revision
        /// </summary>
        public string Revision
        {
            get => _revision;
            set => SetProperty(ref _revision, value);
        }

        /// <summary>
        /// Gets or sets the project date
        /// </summary>
        public DateTime Date
        {
            get => _date;
            set => SetProperty(ref _date, value);
        }

        /// <summary>
        /// Gets the formatted date string
        /// </summary>
        public string DateString => Date.ToString("dd/MM/yy");

        /// <summary>
        /// Gets or sets the ambient temperature
        /// </summary>
        [Range(0, 100)]
        public double AmbientTemp
        {
            get => _ambientTemp;
            set
            {
                if (SetProperty(ref _ambientTemp, value))
                {
                    ParametersChanged = true;
                    OnPropertyChanged(nameof(IsNZCableSelection));
                    OnPropertyChanged(nameof(IsAUSCableSelection));
                }
            }
        }

        /// <summary>
        /// Gets or sets the system maximum voltage drop percentage
        /// </summary>
        [Range(0.01, 0.20)]
        public double SystemVDMaxPerc
        {
            get => _systemVDMaxPerc;
            set
            {
                if (SetProperty(ref _systemVDMaxPerc, value))
                {
                    ParametersChanged = true;
                    OnPropertyChanged(nameof(IsSystemVD5Percent));
                    OnPropertyChanged(nameof(IsSystemVD7Percent));
                }
            }
        }

        /// <summary>
        /// Gets or sets whether system VD is 5%
        /// </summary>
        public bool IsSystemVD5Percent
        {
            get => Math.Abs(SystemVDMaxPerc - 0.05) < 0.001;
            set
            {
                if (value)
                    SystemVDMaxPerc = 0.05;
            }
        }

        /// <summary>
        /// Gets or sets whether system VD is 7%
        /// </summary>
        public bool IsSystemVD7Percent
        {
            get => Math.Abs(SystemVDMaxPerc - 0.07) < 0.001;
            set
            {
                if (value)
                    SystemVDMaxPerc = 0.07;
            }
        }

        /// <summary>
        /// Gets or sets whether NZ cable selection is active (30°C)
        /// </summary>
        public bool IsNZCableSelection
        {
            get => Math.Abs(AmbientTemp - 30.0) < 0.1;
            set
            {
                if (value)
                    AmbientTemp = 30.0;
            }
        }

        /// <summary>
        /// Gets or sets whether AUS cable selection is active (40°C)
        /// </summary>
        public bool IsAUSCableSelection
        {
            get => Math.Abs(AmbientTemp - 40.0) < 0.1;
            set
            {
                if (value)
                    AmbientTemp = 40.0;
            }
        }

        /// <summary>
        /// Gets or sets the discrimination test multiplier
        /// </summary>
        [Range(1.0, 5.0)]
        public double DiscriminationTestMultiplier
        {
            get => _discriminationTestMultiplier;
            set
            {
                if (SetProperty(ref _discriminationTestMultiplier, value))
                    ParametersChanged = true;
            }
        }

        /// <summary>
        /// Gets or sets the CPD range selected index
        /// </summary>
        public int GuiGenCPDRangeSelectedIndex
        {
            get => _guiGenCPDRangeSelectedIndex;
            set
            {
                if (SetProperty(ref _guiGenCPDRangeSelectedIndex, value))
                    ParametersChanged = true;
            }
        }

        /// <summary>
        /// Gets or sets the database path
        /// </summary>
        public string DatabasePath
        {
            get => _databasePath;
            set => SetProperty(ref _databasePath, value);
        }

        /// <summary>
        /// Gets or sets the power voltage drop calculation method
        /// </summary>
        public VoltDropCalculation PowerVDCalculation
        {
            get => _powerVDCalculation;
            set
            {
                if (SetProperty(ref _powerVDCalculation, value))
                    ParametersChanged = true;
            }
        }

        /// <summary>
        /// Gets or sets the lighting voltage drop calculation method
        /// </summary>
        public VoltDropCalculation LightingVDCalculation
        {
            get => _lightingVDCalculation;
            set
            {
                if (SetProperty(ref _lightingVDCalculation, value))
                    ParametersChanged = true;
            }
        }

        /// <summary>
        /// Gets or sets the other voltage drop calculation method
        /// </summary>
        public VoltDropCalculation OtherVDCalculation
        {
            get => _otherVDCalculation;
            set
            {
                if (SetProperty(ref _otherVDCalculation, value))
                    ParametersChanged = true;
            }
        }

        /// <summary>
        /// Gets or sets whether GPO calculation uses 80%
        /// </summary>
        public bool GPOCalc80Perc
        {
            get => _gpoCalc80Perc;
            set
            {
                if (SetProperty(ref _gpoCalc80Perc, value))
                    ParametersChanged = true;
            }
        }

        /// <summary>
        /// Gets or sets whether power clearing time is 0.4s
        /// </summary>
        public bool ClearingTimePower04
        {
            get => _clearingTimePower04;
            set
            {
                if (SetProperty(ref _clearingTimePower04, value))
                    ParametersChanged = true;
            }
        }

        /// <summary>
        /// Gets or sets whether lighting clearing time is 0.4s
        /// </summary>
        public bool ClearingTimeLighting04
        {
            get => _clearingTimeLighting04;
            set
            {
                if (SetProperty(ref _clearingTimeLighting04, value))
                    ParametersChanged = true;
            }
        }

        /// <summary>
        /// Gets or sets whether parameters have been changed
        /// </summary>
        public bool ParametersChanged
        {
            get => _parametersChanged;
            set => SetProperty(ref _parametersChanged, value);
        }

        /// <summary>
        /// Gets or sets whether the database array is populated
        /// </summary>
        public bool DatabaseArrayIsPopulated
        {
            get => _databaseArrayIsPopulated;
            set => SetProperty(ref _databaseArrayIsPopulated, value);
        }

        /// <summary>
        /// Gets or sets the critical error message
        /// </summary>
        public string CriticalErrorMessage
        {
            get => _criticalErrorMessage;
            set => SetProperty(ref _criticalErrorMessage, value);
        }

        /// <summary>
        /// Gets whether there are critical errors
        /// </summary>
        public bool HasCriticalErrors => !string.IsNullOrEmpty(CriticalErrorMessage);

        #endregion

        #region Revit Integration Properties

        /// <summary>
        /// Gets or sets the UIDocument reference
        /// </summary>
        public UIDocument UIDocument { get; set; }

        /// <summary>
        /// Gets or sets the Document reference
        /// </summary>
        public Document Document { get; set; }

        /// <summary>
        /// Gets or sets the task logger
        /// </summary>
        public BecaActivityLoggerData TaskLogger { get; set; }

        #endregion

        #region Constructor

        /// <summary>
        /// Initializes a new instance of PowerBIMProjectInfoModel
        /// </summary>
        public PowerBIMProjectInfoModel()
        {
            // Set default values
            Date = DateTime.Now;
            AmbientTemp = 30.0;
            SystemVDMaxPerc = 0.05;
            DiscriminationTestMultiplier = 1.25;
            PowerVDCalculation = VoltDropCalculation.LinearDeprecating;
            LightingVDCalculation = VoltDropCalculation.LinearDeprecating;
            OtherVDCalculation = VoltDropCalculation.LinearDeprecating;
            ClearingTimePower04 = true;
            ClearingTimeLighting04 = true;
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Creates a deep copy of this project info model
        /// </summary>
        public PowerBIMProjectInfoModel Clone()
        {
            return new PowerBIMProjectInfoModel
            {
                JobName = this.JobName,
                JobNumber = this.JobNumber,
                Engineer = this.Engineer,
                Verifier = this.Verifier,
                Revision = this.Revision,
                DatabasePath = this.DatabasePath,
                DiscriminationTestMultiplier = this.DiscriminationTestMultiplier,
                AmbientTemp = this.AmbientTemp,
                GPOCalc80Perc = this.GPOCalc80Perc,
                ParametersChanged = this.ParametersChanged
            };
        }

        /// <summary>
        /// Copies values from another project info model
        /// </summary>
        public void CopyFrom(PowerBIMProjectInfoModel other)
        {
            if (other == null) return;

            JobName = other.JobName;
            JobNumber = other.JobNumber;
            Engineer = other.Engineer;
            Verifier = other.Verifier;
            Revision = other.Revision;
            DatabasePath = other.DatabasePath;
            DiscriminationTestMultiplier = other.DiscriminationTestMultiplier;
            AmbientTemp = other.AmbientTemp;
            GPOCalc80Perc = other.GPOCalc80Perc;
            ParametersChanged = other.ParametersChanged;
        }

        #endregion
    }
}
