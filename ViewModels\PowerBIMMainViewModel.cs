﻿using Autodesk.Revit.UI;
using BecaActivityLogger.CoreLogic.Data;
using CommunityToolkit.Mvvm.Input;
using MEP.PowerBIM_6.Models;
using MEP.PowerBIM_6.Services;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Input;

namespace MEP.PowerBIM_6.ViewModels
{
    public class PowerBIMMainViewModel : BaseViewModel
    {
        private readonly ExternalEvent _externalEvent;
        private readonly RequestHandler _requestHandler;
        private readonly BecaActivityLoggerData _logger;
        private readonly UIApplication _uiApplication;

        private ObservableCollection<PowerBIMDBDataModel> _distributionBoards;
        private ObservableCollection<PowerBIMDBDataModel> _selectedDistributionBoards;
        private bool _systemVD5Percent = true;
        private bool _systemVD7Percent;
        private bool _nzCableSelection = true;
        private bool _ausCableSelection;

        public PowerBIMMainViewModel(
            ExternalEvent externalEvent,
            RequestHandler requestHandler,
            BecaActivityLoggerData logger,
            UIApplication uiApplication)
        {
            _externalEvent = externalEvent;
            _requestHandler = requestHandler;
            _logger = logger;
            _uiApplication = uiApplication;

            _distributionBoards = new ObservableCollection<PowerBIMDBDataModel>();
            _selectedDistributionBoards = new ObservableCollection<PowerBIMDBDataModel>();

            InitializeCommands();
        }

        #region Properties

        /// <summary>
        /// Collection of all distribution boards in the project
        /// </summary>
        public ObservableCollection<PowerBIMDBDataModel> DistributionBoards
        {
            get => _distributionBoards;
            set => SetProperty(ref _distributionBoards, value);
        }

        /// <summary>
        /// Collection of selected distribution boards
        /// </summary>
        public ObservableCollection<PowerBIMDBDataModel> SelectedDistributionBoards
        {
            get => _selectedDistributionBoards;
            set => SetProperty(ref _selectedDistributionBoards, value);
        }

        /// <summary>
        /// System voltage drop 5% setting
        /// </summary>
        public bool SystemVD5Percent
        {
            get => _systemVD5Percent;
            set => SetProperty(ref _systemVD5Percent, value);
        }

        /// <summary>
        /// System voltage drop 7% setting
        /// </summary>
        public bool SystemVD7Percent
        {
            get => _systemVD7Percent;
            set => SetProperty(ref _systemVD7Percent, value);
        }

        /// <summary>
        /// NZ cable selection (30°C)
        /// </summary>
        public bool NZCableSelection
        {
            get => _nzCableSelection;
            set => SetProperty(ref _nzCableSelection, value);
        }

        /// <summary>
        /// AUS cable selection (40°C)
        /// </summary>
        public bool AUSCableSelection
        {
            get => _ausCableSelection;
            set => SetProperty(ref _ausCableSelection, value);
        }

        #endregion

        #region Commands

        public ICommand RunAutoSizerCommand { get; private set; }
        public ICommand SaveCommand { get; private set; }
        public ICommand ExportCommand { get; private set; }
        public ICommand HelpCommand { get; private set; }

        private void InitializeCommands()
        {
            RunAutoSizerCommand = new AsyncRelayCommand(RunAutoSizerAsync);
            SaveCommand = new AsyncRelayCommand(SaveAsync);
            ExportCommand = new RelayCommand(Export);
            HelpCommand = new RelayCommand(ShowHelp);
        }

        #endregion

        #region Command Implementations

        private async Task RunAutoSizerAsync()
        {
            SetBusyState(true, "Running auto sizer...");
            try
            {
                // TODO: Implement auto sizer logic
                await Task.Delay(1000); // Placeholder
                StatusMessage = "Auto sizer completed";
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error: {ex.Message}";
            }
            finally
            {
                SetBusyState(false);
            }
        }

        private async Task SaveAsync()
        {
            SetBusyState(true, "Saving...");
            try
            {
                // TODO: Implement save logic
                await Task.Delay(500); // Placeholder
                StatusMessage = "Saved successfully";
            }
            catch (Exception ex)
            {
                StatusMessage = $"Save error: {ex.Message}";
            }
            finally
            {
                SetBusyState(false);
            }
        }

        private void Export()
        {
            // TODO: Implement export logic
            StatusMessage = "Export functionality not yet implemented";
        }

        private void ShowHelp()
        {
            // TODO: Implement help dialog
            StatusMessage = "Help functionality not yet implemented";
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Initialize the ViewModel with project data
        /// </summary>
        public void Initialize()
        {
            // TODO: Load distribution boards from project
            StatusMessage = "Initialized";
        }

        /// <summary>
        /// Called when the main window is closing
        /// </summary>
        public void OnWindowClosing()
        {
            // TODO: Cleanup logic
        }

        #endregion
    }
}
