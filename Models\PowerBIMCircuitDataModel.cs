﻿using Autodesk.Revit.DB.Electrical;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MEP.PowerBIM_6.Models
{
    /// <summary>
    /// Model representing a circuit for WPF data binding
    /// </summary>
    public class PowerBIMCircuitDataModel : ObservableObject
    {
        #region Private Fields

        private string _cctNumber = string.Empty;
        private string _scheduleDescription = string.Empty;
        private double _revitCurrent;
        private bool _manualCurrent;
        private double _manualPowerBimUserCurrent;
        private double _cctPowerBIMCurrent;
        private int _numberOfPoles = 1;
        private int _numberOfElements = 1;
        private string _scheduleCableToFirst = string.Empty;
        private string _scheduleCableToFinal = string.Empty;
        private string _scheduleProtectiveDevice = string.Empty;
        private string _scheduleCurveType = string.Empty;
        private double _scheduleTripRating;
        private string _checkResult = string.Empty;
        private bool _cctIsSpareOrSpace;
        private bool _cctIsPower;
        private bool _cctIsLighting;
        private bool _cctGPOPresent;
        private bool _cctRCDElementIsPresent;
        private string _cctRCDName = string.Empty;
        private int _gpoCount;
        private double _cctClearingTime;
        private bool _dataGood = true;
        private string _errorMessage = string.Empty;
        private bool _parametersGood = true;
        private bool _valuesMissing;
        private bool _isLocked;
        private bool _circuitLengthIsManual;

        #endregion

        #region Constructor

        public PowerBIMCircuitDataModel()
        {
            // Set default values
            Number_Of_Poles = 1;
            Number_Of_Elements = 1;
            DataGood = true;
            ParametersGood = true;
        }

        #endregion

        #region Basic Properties

        /// <summary>
        /// Gets or sets the circuit number
        /// </summary>
        [Required]
        public string CCT_Number
        {
            get => _cctNumber;
            set => SetProperty(ref _cctNumber, value);
        }

        /// <summary>
        /// Gets or sets the schedule description
        /// </summary>
        public string Schedule_Description
        {
            get => _scheduleDescription;
            set => SetProperty(ref _scheduleDescription, value);
        }

        /// <summary>
        /// Gets or sets the Revit current
        /// </summary>
        [Range(0, double.MaxValue)]
        public double Revit_Current
        {
            get => _revitCurrent;
            set => SetProperty(ref _revitCurrent, value);
        }

        /// <summary>
        /// Gets or sets whether manual current is used
        /// </summary>
        public bool ManualCurrent
        {
            get => _manualCurrent;
            set => SetProperty(ref _manualCurrent, value);
        }

        /// <summary>
        /// Gets or sets the manual PowerBIM user current
        /// </summary>
        [Range(0, double.MaxValue)]
        public double Manual_PowerBim_User_Current
        {
            get => _manualPowerBimUserCurrent;
            set => SetProperty(ref _manualPowerBimUserCurrent, value);
        }

        /// <summary>
        /// Gets or sets the circuit PowerBIM current
        /// </summary>
        [Range(0, double.MaxValue)]
        public double CCT_PowerBIM_Current
        {
            get => _cctPowerBIMCurrent;
            set => SetProperty(ref _cctPowerBIMCurrent, value);
        }

        /// <summary>
        /// Gets or sets the number of poles
        /// </summary>
        [Range(1, 3)]
        public int Number_Of_Poles
        {
            get => _numberOfPoles;
            set => SetProperty(ref _numberOfPoles, value);
        }

        /// <summary>
        /// Gets or sets the number of elements
        /// </summary>
        [Range(1, int.MaxValue)]
        public int Number_Of_Elements
        {
            get => _numberOfElements;
            set => SetProperty(ref _numberOfElements, value);
        }

        #endregion

        #region Cable Properties

        /// <summary>
        /// Gets or sets the schedule cable to first
        /// </summary>
        public string Schedule_Cable_To_First
        {
            get => _scheduleCableToFirst;
            set => SetProperty(ref _scheduleCableToFirst, value);
        }

        /// <summary>
        /// Gets or sets the schedule cable to final
        /// </summary>
        public string Schedule_Cable_To_Final
        {
            get => _scheduleCableToFinal;
            set => SetProperty(ref _scheduleCableToFinal, value);
        }

        /// <summary>
        /// Gets or sets whether circuit length is manual
        /// </summary>
        public bool CircuitLengthIsManual
        {
            get => _circuitLengthIsManual;
            set => SetProperty(ref _circuitLengthIsManual, value);
        }

        #endregion

        #region Protection Properties

        /// <summary>
        /// Gets or sets the schedule protective device
        /// </summary>
        public string Schedule_Protective_Device
        {
            get => _scheduleProtectiveDevice;
            set => SetProperty(ref _scheduleProtectiveDevice, value);
        }

        /// <summary>
        /// Gets or sets the schedule curve type
        /// </summary>
        public string Schedule_Curve_Type
        {
            get => _scheduleCurveType;
            set => SetProperty(ref _scheduleCurveType, value);
        }

        /// <summary>
        /// Gets or sets the schedule trip rating
        /// </summary>
        [Range(0, double.MaxValue)]
        public double Schedule_Trip_Rating
        {
            get => _scheduleTripRating;
            set => SetProperty(ref _scheduleTripRating, value);
        }

        #endregion

        #region Circuit Type Properties

        /// <summary>
        /// Gets or sets whether the circuit is spare or space
        /// </summary>
        public bool CCT_Is_Spare_Or_Space
        {
            get => _cctIsSpareOrSpace;
            set => SetProperty(ref _cctIsSpareOrSpace, value);
        }

        /// <summary>
        /// Gets or sets whether the circuit is power
        /// </summary>
        public bool CCT_Is_Power
        {
            get => _cctIsPower;
            set => SetProperty(ref _cctIsPower, value);
        }

        /// <summary>
        /// Gets or sets whether the circuit is lighting
        /// </summary>
        public bool CCT_Is_Lighting
        {
            get => _cctIsLighting;
            set => SetProperty(ref _cctIsLighting, value);
        }

        /// <summary>
        /// Gets or sets whether GPO is present
        /// </summary>
        public bool CCT_GPO_Present
        {
            get => _cctGPOPresent;
            set => SetProperty(ref _cctGPOPresent, value);
        }

        /// <summary>
        /// Gets or sets whether RCD element is present
        /// </summary>
        public bool CCT_RCD_ElementIsPresent
        {
            get => _cctRCDElementIsPresent;
            set => SetProperty(ref _cctRCDElementIsPresent, value);
        }

        /// <summary>
        /// Gets or sets the RCD name
        /// </summary>
        public string CCT_RCD_Name
        {
            get => _cctRCDName;
            set => SetProperty(ref _cctRCDName, value);
        }

        /// <summary>
        /// Gets or sets the GPO count
        /// </summary>
        [Range(0, int.MaxValue)]
        public int GPO_Count
        {
            get => _gpoCount;
            set => SetProperty(ref _gpoCount, value);
        }

        /// <summary>
        /// Gets or sets the circuit clearing time
        /// </summary>
        [Range(0, double.MaxValue)]
        public double CCT_Clearing_Time
        {
            get => _cctClearingTime;
            set => SetProperty(ref _cctClearingTime, value);
        }

        #endregion

        #region Status Properties

        /// <summary>
        /// Gets or sets the check result
        /// </summary>
        public string CheckResult
        {
            get => _checkResult;
            set => SetProperty(ref _checkResult, value);
        }

        /// <summary>
        /// Gets or sets whether the data is good
        /// </summary>
        public bool DataGood
        {
            get => _dataGood;
            set => SetProperty(ref _dataGood, value);
        }

        /// <summary>
        /// Gets or sets the error message
        /// </summary>
        public string ErrorMessage
        {
            get => _errorMessage;
            set => SetProperty(ref _errorMessage, value);
        }

        /// <summary>
        /// Gets or sets whether parameters are good
        /// </summary>
        public bool ParametersGood
        {
            get => _parametersGood;
            set => SetProperty(ref _parametersGood, value);
        }

        /// <summary>
        /// Gets or sets whether values are missing
        /// </summary>
        public bool ValuesMissing
        {
            get => _valuesMissing;
            set => SetProperty(ref _valuesMissing, value);
        }

        /// <summary>
        /// Gets or sets whether the circuit is locked
        /// </summary>
        public bool IsLocked
        {
            get => _isLocked;
            set => SetProperty(ref _isLocked, value);
        }

        #endregion

        #region Revit Integration Properties

        /// <summary>
        /// Gets or sets the circuit element
        /// </summary>
        public Element CCT_Element { get; set; }

        /// <summary>
        /// Gets or sets the electrical system
        /// </summary>
        public ElectricalSystem CCT_Electrical_System { get; set; }

        /// <summary>
        /// Gets or sets the parent DB reference
        /// </summary>
        public PowerBIMDBDataModelEnhanced ParentDB { get; set; }

        /// <summary>
        /// Gets or sets the project info reference
        /// </summary>
        public PowerBIMProjectInfoModel ProjectInfo { get; set; }

        #endregion

        #region Computed Properties

        /// <summary>
        /// Gets the effective current (manual or calculated)
        /// </summary>
        public double EffectiveCurrent
        {
            get
            {
                if (ManualCurrent && Manual_PowerBim_User_Current > 0)
                    return Manual_PowerBim_User_Current;
                return CCT_PowerBIM_Current > 0 ? CCT_PowerBIM_Current : Revit_Current;
            }
        }

        /// <summary>
        /// Gets the circuit type description
        /// </summary>
        public string CircuitTypeDescription
        {
            get
            {
                if (CCT_Is_Spare_Or_Space) return "Spare/Space";
                if (CCT_Is_Power) return "Power";
                if (CCT_Is_Lighting) return "Lighting";
                return "Other";
            }
        }

        /// <summary>
        /// Gets the status color for UI display
        /// </summary>
        public string StatusColor
        {
            get
            {
                if (!DataGood || ValuesMissing) return "Red";
                if (CheckResult == "Fail") return "Red";
                if (CheckResult == "Warning") return "Orange";
                if (CheckResult == "Pass") return "Green";
                return "Gray";
            }
        }

        #endregion
    }
}
